import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of } from 'rxjs';
import { FundKpiComponent } from './fund-kpi.component';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ToastrService } from 'ngx-toastr';
import { FundService } from 'src/app/services/funds.service';

describe('FundKpiComponent', () => {
  let component: FundKpiComponent;
  let fixture: ComponentFixture<FundKpiComponent>;

  beforeEach(async () => {
    const mockMiscService = jasmine.createSpyObj('MiscellaneousService', ['getMessageTimeSpan']);
    const mockPortfolioCompanyService = jasmine.createSpyObj('PortfolioCompanyService', ['getPortfolioCompany', 'getfinancialsvalueTypes']);
    mockPortfolioCompanyService.getfinancialsvalueTypes.and.returnValue(of([]));
    const mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error']);
    const mockFundService = jasmine.createSpyObj('FundService', ['getFundKPIValues']);

    mockMiscService.getMessageTimeSpan.and.returnValue(5000);
    mockFundService.getFundKPIValues.and.returnValue(of({}));

    await TestBed.configureTestingModule({
      declarations: [ FundKpiComponent ],
      providers: [
        { provide: MiscellaneousService, useValue: mockMiscService },
        { provide: PortfolioCompanyService, useValue: mockPortfolioCompanyService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: FundService, useValue: mockFundService }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FundKpiComponent);
    component = fixture.componentInstance;
    // Set required inputs to prevent errors
    component.modelList = { currencyDetail: { currencyCode: 'USD' } };
    component.fundId = 1;
    component.moduleId = 1;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});