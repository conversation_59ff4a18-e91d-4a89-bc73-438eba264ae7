import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA ,ChangeDetectorRef} from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { PermissionService } from 'src/app/services/permission.service';
import { FormsModule } from '@angular/forms';
import { WorkflowFeaturesComponent } from './workflow-features.component';
import { of, throwError } from 'rxjs';

describe('WorkflowFeaturesComponent', () => {
  let component: WorkflowFeaturesComponent;
  let fixture: ComponentFixture<WorkflowFeaturesComponent>;

  beforeEach(() => {
    const changeDetectorRefStub = () => ({ detectChanges: () => ({}) });
    const toastrServiceStub = () => ({
      overlayContainer: {},
      error: (response, string, object) => ({}),
      success: (string, string1, object) => ({})
    });
    const permissionServiceStub = () => ({
      getGroupById: object => ({ subscribe: f => f({}) }),
      isFullAccess: groupId => ({ subscribe: f => f({}) }),
      updateGroup: modelClone => ({ subscribe: f => f({}) }),
      getFeatureList: object => ({ subscribe: f => f({}) })
    });
    TestBed.configureTestingModule({
      imports: [FormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [WorkflowFeaturesComponent],
      providers: [
        { provide: ChangeDetectorRef, useFactory: changeDetectorRefStub },
        { provide: ToastrService, useFactory: toastrServiceStub },
        { provide: PermissionService, useFactory: permissionServiceStub }
      ]
    });
    fixture = TestBed.createComponent(WorkflowFeaturesComponent);
    component = fixture.componentInstance;
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`featureListOriginal has default value`, () => {
    expect(component.featureListOriginal).toEqual([]);
  });

  it(`isDisabledBtn has default value`, () => {
    expect(component.isDisabledBtn).toEqual(false);
  });

  it(`mappedFeatureList has default value`, () => {
    expect(component.mappedFeatureList).toEqual([]);
  });

  it(`isLoader has default value`, () => {
    expect(component.isLoader).toEqual(true);
  });

  it(`featuresToBeUpdated has default value`, () => {
    expect(component.featuresToBeUpdated).toEqual([]);
  });

  it(`groupStatus has default value`, () => {
    expect(component.groupStatus).toEqual(true);
  });

  it(`enableFeature has default value`, () => {
    expect(component.enableFeature).toEqual(true);
  });

  it(`msgs has default value`, () => {
    expect(component.msgs).toEqual([]);
  });

  it(`successMessage has default value`, () => {
    expect(component.successMessage).toEqual(true);
  });

  it(`loading has default value`, () => {
    expect(component.loading).toEqual(false);
  });

  it(`selectedValues has default value`, () => {
    expect(component.selectedValues).toEqual([]);
  });

  it(`isConfirmFullAccess has default value`, () => {
    expect(component.isConfirmFullAccess).toEqual(false);
  });

  it(`featureFullAccess has default value`, () => {
    expect(component.featureFullAccess).toEqual(false);
  });

  it(`loadingMap has default value`, () => {
    expect(component.loadingMap).toEqual(false);
  });

  it('#ngOnInit should initialize cols, scrollableCols, frozenCols, and toastrService.overlayContainer', () => {
    const expectedCols = [
      { field: 'canAdd', header: 'Add' },
      { field: 'canEdit', header: 'Edit' },
      { field: 'canView', header: 'View' },
      { field: 'canExport', header: 'Export' },
      { field: 'canImport', header: 'Import' }
    ];
    const expectedFrozenCols = [
      { field: "checkbox", header: "checkbox" },
      { field: "features", header: "Features" }
    ];
    const toasterService = TestBed.inject(ToastrService);

    component.ngOnInit();

    expect(component.cols).toEqual(expectedCols);
    expect(component.scrollableCols).toEqual(expectedCols);
    expect(component.frozenCols).toEqual(expectedFrozenCols);
    expect(toasterService.overlayContainer).toBe(component.toastContainer);
  });

  it('#ngOnChanges should update groupData.groupID, call getFeatureList and setDefaultValues, and set isDisabledBtn and isLoader to true', () => {
    component.groupData = { groupID: 'oldID' };
    component.groupId = 'newID';
    spyOn(component, 'getFeatureList');
    spyOn(component, 'setDefaultValues');
    component.ngOnChanges();

    expect(component.groupData.groupID).toEqual('newID');
    expect(component.getFeatureList).toHaveBeenCalled();
    expect(component.setDefaultValues).toHaveBeenCalled();
    expect(component.isDisabledBtn).toBeTrue();
    expect(component.isLoader).toBeTrue();
  });

  it('#checkAnyDataChange should set isDisabledBtn to false', () => {
    component.isDisabledBtn = true;
    component.checkAnyDataChange({}, {});

    expect(component.isDisabledBtn).toBeFalse();
  });

  it('#setDefaultValues should handle successful getGroupById call', (done) => {
    const groupId = '1';
    const resp = { groupID: 1, selectedFeatures: [] };
    const result = { body: resp };
    const mockPermissionService = TestBed.inject(PermissionService);
    spyOn(component, 'createHierarchicalFeatureSelection').and.callFake((features) => features);
    spyOn(component, 'enableDisableFeatures');
    spyOn(mockPermissionService, 'getGroupById').and.returnValue(of(result));
    component.groupId = groupId;

    component.setDefaultValues();

    setTimeout(() => {
      expect(component.loading).toBeFalse();
      expect(component.model).toEqual(resp);
      expect(component.selectedFeatureList).toEqual(resp.selectedFeatures);
      expect(component.featureListOriginal).toEqual(resp.selectedFeatures);
      expect(component.createHierarchicalFeatureSelection).toHaveBeenCalledWith(resp.selectedFeatures, null);
      expect(component.enableDisableFeatures).toHaveBeenCalled();
      done();
    });
  });

  it('#setDefaultValues should handle successful getGroupById call', (done) => {
    const groupId = '1';
    const resp = { groupID: 1, selectedFeatures: [] };
    const result = { body: resp };
    const mockPermissionService = TestBed.inject(PermissionService);
    spyOn(component, 'createHierarchicalFeatureSelection').and.callFake((features) => features);
    spyOn(component, 'enableDisableFeatures');
    spyOn(mockPermissionService, 'getGroupById').and.returnValue(of(result));
    component.groupId = groupId;

    component.setDefaultValues();

    setTimeout(() => {
      expect(component.loading).toBeFalse();
      expect(component.model).toEqual(resp);
      expect(component.selectedFeatureList).toEqual(resp.selectedFeatures);
      expect(component.featureListOriginal).toEqual(resp.selectedFeatures);
      expect(component.createHierarchicalFeatureSelection).toHaveBeenCalledWith(resp.selectedFeatures, null);
      expect(component.enableDisableFeatures).toHaveBeenCalled();
      done();
    });
  });

  it('#setDefaultValues should handle unsuccessful getGroupById call', (done) => {
    const groupId = '1';
    const resp = { groupID: 0, response: 'Error' };
    const result = { body: resp };
    const mockPermissionService = TestBed.inject(PermissionService);
    const mockToastrService = TestBed.inject(ToastrService);
    spyOn(mockToastrService, 'error');
    spyOn(mockPermissionService, 'getGroupById').and.returnValue(of(result));
    component.groupId = groupId;

    component.setDefaultValues();

    setTimeout(() => {
      expect(component.loading).toBeFalse();
      expect(mockToastrService.error).toHaveBeenCalledWith(resp.response, "", { positionClass: "toast-center-center" });
      done();
    });
  });

  it('#setDefaultValues should handle error during getGroupById call', (done) => {
    const groupId = '1';
    const mockPermissionService = TestBed.inject(PermissionService);
    spyOn(mockPermissionService, 'getGroupById').and.returnValue(throwError(() =>'Error'));
    component.groupId = groupId;

    component.setDefaultValues();

    setTimeout(() => {
      expect(component.loading).toBeFalse();
      done();
    }, 100);
  });

  it('#setDefaultValues should set loading to false if groupId is undefined or 0', () => {
    component.groupId = undefined;

    component.setDefaultValues();

    expect(component.loading).toBeFalse();
  });

  it('#isFullAccess should handle successful isFullAccess call', (done) => {
    const result = true;
    const mockPermissionService = TestBed.inject(PermissionService);
    component.groupId = '1';
    component.featureList = [{ data: { features: { feature: 'Portfolio Company' } } }];
    spyOn(mockPermissionService, 'isFullAccess').and.returnValue(of(result));

    component.isFullAccess();

    setTimeout(() => {
      expect(component.featureFullAccess).toBe(result);
      expect(component.featureList[0].data.isFullAccess).toBe(result);
      done();
    });
  });

  it('#createHierarchicalFeatureSelection should return null if featureList is null', () => {
    const result = component.createHierarchicalFeatureSelection(null, {});

    expect(result).toBeNull();
  });

  it('#createHierarchicalFeature should create hierarchical feature', () => {
    const featureList = [
      { id: 2, children: [{ id: 3 }] },
      { id: 4, children: [] },
      { id: 5 }
    ];
    const expectedFeatureList = [
      { id: 2, children: [{ id: 3, parent: null }], parent: null },
      { id: 4, children: [], parent: null },
      { id: 5, parent: null },
      { id: 3, parent: null }
    ];

    const result = component.createHierarchicalFeature(featureList, {});

    expect(result).toEqual(expectedFeatureList);
  });

  it('#createHierarchicalFeature should return null if featureList is null', () => {
    const result = component.createHierarchicalFeature(null, {});

    expect(result).toBeNull();
  });

  it('#resetMapping should reset mapping', () => {
    component.featureList = <any>[1, 2, 3];
    component.featureListOriginal = [1, 2, 3];
    component.isDisabledBtn = false;
    spyOn(component, 'getFeatureList');
    spyOn(component, 'setDefaultValues');

    component.resetMapping();

    expect(component.featureList).toEqual([]);
    expect(component.featureListOriginal).toEqual([]);
    expect(component.getFeatureList).toHaveBeenCalled();
    expect(component.setDefaultValues).toHaveBeenCalled();
    expect(component.isDisabledBtn).toBeTrue();
  });

  it('#getFeatureList should handle successful getFeatureList call', (done) => {
    const result = { 
      body: [
        { data: { id: 1 }, children: [] },
        { data: { id: 2 }, children: [] },
        { data: { id: 3 }, children: [] }
      ] 
    };
    const mockPermissionService = TestBed.inject(PermissionService);
    spyOn(component, 'enableDisableFeatures');
    spyOn(component, 'isFullAccess');
    spyOn(mockPermissionService, 'getFeatureList').and.returnValue(of(result));
  

    component.getFeatureList();

    setTimeout(() => {
      expect(component.featureList).toEqual(result.body);
      expect(component.featureListOriginal).toEqual(result.body);
      expect(component.enableDisableFeatures).toHaveBeenCalled();
      expect(component.isFullAccess).toHaveBeenCalled();
      expect(component.isLoader).toBeFalse();
      done();
    });
  });

  it('#getFeatureList should handle error during getFeatureList call', (done) => {
    const mockPermissionService = TestBed.inject(PermissionService);
    spyOn(mockPermissionService, 'getFeatureList').and.returnValue(throwError(() =>'Error'));

    component.getFeatureList();

    setTimeout(() => {
      expect(component.isLoader).toBeFalse();
      done();
    });
  });

  it('#enableDisableMainFeatureList should enable or disable main feature list', () => {
    component.model = {
      selectedFeatures: [
        { data: { features: { featureId: 1 } } },
        { data: { features: { featureId: 2 } } }
      ]
    };
    const featureList = [
      { data: { features: { featureId: 1 }, featureEnabled: false }, children: [] },
      { data: { features: { featureId: 2 }, featureEnabled: false }, children: [] },
      { data: { features: { featureId: 3 }, featureEnabled: false }, children: [] }
    ];

    const result = component.enableDisableMainFeatureList(featureList);

    expect(result[0].data.featureEnabled).toBeTrue();
    expect(result[1].data.featureEnabled).toBeTrue();
    expect(result[2].data.featureEnabled).toBeFalse();
  });

  it('#enableDisableFeatures should enable or disable features', () => {
    component.model = {
      selectedFeatures: [
        { data: { featureEnabled: false } },
        { data: { featureEnabled: false } }
      ]
    };
  
    const featureList = [
      { data: { features: { featureId: 1 }, featureEnabled: false }, children: [] },
      { data: { features: { featureId: 2 }, featureEnabled: false }, children: [] },
      { data: { features: { featureId: 3 }, featureEnabled: false }, children: [] }
    ];
    component.featureList = [...featureList]; // Set component.featureList
  
    spyOn(component, 'enableDisableMainFeatureList').and.returnValue(featureList);
    spyOn(component, 'checkMatchedFeatureInHierarchy');
  
    component.enableDisableFeatures();
  
    expect(component.enableDisableMainFeatureList).toHaveBeenCalledWith(featureList);
    expect(component.checkMatchedFeatureInHierarchy).toHaveBeenCalledTimes(component.model.selectedFeatures.length);
    expect(component.model.selectedFeatures.every(feature => feature.data.featureEnabled)).toBeFalse();
  });

  it('#isActionHidden should return false if action is in enabledActions', () => {
    const feature = {
      enabledActions: [
        { action: 'action1' },
        { action: 'action2' },
        { action: 'action3' }
      ]
    };
    const action = 'action2';

    const result = component.isActionHidden(feature, action);

    expect(result).toBeFalse();
  });

  it('#isActionHidden should return true if action is not in enabledActions', () => {
    const feature = {
      enabledActions: [
        { action: 'action1' },
        { action: 'action2' },
        { action: 'action3' }
      ]
    };
    const action = 'action4';

    const result = component.isActionHidden(feature, action);

    expect(result).toBeTrue();
  });

  it('#checkMatchedFeatureInHierarchy should return true if feature is in hierarchy', () => {
    const featureList = [
      { data: { features: { featureId: 1 } }, children: [] },
      { data: { features: { featureId: 2 } }, children: [
        { data: { features: { featureId: 3 } }, children: [] }
      ] }
    ];
    const featureSelected = { data: { features: { featureId: 3 } }, parent: null };

    const result = component.checkMatchedFeatureInHierarchy(featureList, featureSelected);

    expect(result).toBeTrue();
    expect(featureSelected.parent).toEqual(featureList[1]);
    expect(featureSelected.parent.partialSelected).toBeTrue();
  });

  it('#checkMatchedFeatureInHierarchy should return false if feature is not in hierarchy', () => {
    const featureList = [
      { data: { features: { featureId: 1 } }, children: [] },
      { data: { features: { featureId: 2 } }, children: [] }
    ];
    const featureSelected = { data: { features: { featureId: 3 } }, parent: null };

    const result = component.checkMatchedFeatureInHierarchy(featureList, featureSelected);

    expect(result).toBeFalse();
    expect(featureSelected.parent).toBeNull();
  });

  it('#unSelectAllActionsInFeatures should unselect all actions in features', () => {
    const featureList = [
      { data: { features: { featureId: 1 }, featureEnabled: true, canAdd: true, canEdit: true, canView: true, canExport: true, canImport: true }, children: [] },
      { data: { features: { featureId: 2 }, featureEnabled: true, canAdd: true, canEdit: true, canView: true, canExport: true, canImport: true }, children: [] },
      { data: { features: { featureId: 3 }, featureEnabled: true, canAdd: true, canEdit: true, canView: true, canExport: true, canImport: true }, children: [] }
    ];

    component.model = {
      selectedFeatures: [
        { data: { features: { featureId: 1 } } },
        { data: { features: { featureId: 2 } } }
      ]
    };

    component.unSelectAllActionsInFeatures(featureList);

    expect(featureList[0].data.featureEnabled).toBeTrue();
    expect(featureList[0].data.canAdd).toBeTrue();
    expect(featureList[0].data.canEdit).toBeTrue();
    expect(featureList[0].data.canView).toBeTrue();
    expect(featureList[0].data.canExport).toBeTrue();
    expect(featureList[0].data.canImport).toBeTrue();

    expect(featureList[1].data.featureEnabled).toBeTrue();
    expect(featureList[1].data.canAdd).toBeTrue();
    expect(featureList[1].data.canEdit).toBeTrue();
    expect(featureList[1].data.canView).toBeTrue();
    expect(featureList[1].data.canExport).toBeTrue();
    expect(featureList[1].data.canImport).toBeTrue();

    expect(featureList[2].data.featureEnabled).toBe(null);
    expect(featureList[2].data.canAdd).toBe(null);
    expect(featureList[2].data.canEdit).toBe(null);
    expect(featureList[2].data.canView).toBe(null);
    expect(featureList[2].data.canExport).toBe(null);
    expect(featureList[2].data.canImport).toBe(null);
  });

  it('#uncheckAllActionsInFeatures should uncheck all actions in features', () => {
    const featureList = [
      { data: { features: { featureId: 1 }, featureEnabled: true, canAdd: true, canEdit: true, canView: true, canExport: true, canImport: true }, children: [] },
      { data: { features: { featureId: 2 }, featureEnabled: true, canAdd: true, canEdit: true, canView: true, canExport: true, canImport: true }, children: [
        { data: { features: { featureId: 3 }, featureEnabled: true, canAdd: true, canEdit: true, canView: true, canExport: true, canImport: true }, children: [] }
      ] }
    ];

    component.uncheckAllActionsInFeatures(featureList);

    expect(featureList[0].data.featureEnabled).toBeNull();
    expect(featureList[0].data.canAdd).toBeNull();
    expect(featureList[0].data.canEdit).toBeNull();
    expect(featureList[0].data.canView).toBeNull();
    expect(featureList[0].data.canExport).toBeNull();
    expect(featureList[0].data.canImport).toBeNull();

    expect(featureList[1].data.featureEnabled).toBeNull();
    expect(featureList[1].data.canAdd).toBeNull();
    expect(featureList[1].data.canEdit).toBeNull();
    expect(featureList[1].data.canView).toBeNull();
    expect(featureList[1].data.canExport).toBeNull();
    expect(featureList[1].data.canImport).toBeNull();

    expect(featureList[1].children[0].data.featureEnabled).toBeNull();
    expect(featureList[1].children[0].data.canAdd).toBeNull();
    expect(featureList[1].children[0].data.canEdit).toBeNull();
    expect(featureList[1].children[0].data.canView).toBeNull();
    expect(featureList[1].children[0].data.canExport).toBeNull();
    expect(featureList[1].children[0].data.canImport).toBeNull();
  });

  it('#serializeFeatureList should serialize feature list', () => {
    const featureList = [
      { data: { features: { featureId: 1 } }, children: [] },
      { data: { features: { featureId: 2 } }, children: [
        { data: { features: { featureId: 3 } }, children: [] }
      ] }
    ];

    const result = component.serializeFeatureList(featureList);

    expect(result).toEqual([
      { data: { features: { featureId: 1 } }, children: null },
      { data: { features: { featureId: 2 } }, children: [
        { data: { features: { featureId: 3 } }, children: null }
      ] }
    ]);
  });

  it('#formReset should reset form', () => {
    component.groupStatus = true;
    component.model = {};
    component.featureList = [{}];
    component.isDisabledBtn = false;
    spyOn(component, 'getFeatureList');
    spyOn(component, 'setDefaultValues');
    component.formReset();

    expect(component.model).toEqual({
      isActive: true,
      userGroup: []
    });
    expect(component.featureList).toEqual([]);
    expect(component.getFeatureList).toHaveBeenCalled();
    expect(component.setDefaultValues).toHaveBeenCalled();
    expect(component.isDisabledBtn).toBe(true);
  });

  it('#setFullAccess should set full access', () => {
    const rowData = { isFullAccess: true };

    component.setFullAccess(rowData);

    expect(component.isConfirmFullAccess).toBe(true);
    expect(component.selectedFeature).toBe(rowData);
    expect(component.message).toBe("Are you sure you want to provide full access to the feature and the sub features under it?");

    rowData.isFullAccess = false;

    component.setFullAccess(rowData);

    expect(component.isConfirmFullAccess).toBe(true);
    expect(component.selectedFeature).toBe(rowData);
    expect(component.message).toBe("Are you sure you want to revoke access to the feature and the sub features under it?");
  });

  it('#OnFullAccess should set isDisabledBtn and isConfirmFullAccess to false', () => {
    component.OnFullAccess();

    expect(component.isDisabledBtn).toBe(false);
    expect(component.isConfirmFullAccess).toBe(false);
  });

  it('#OnCancelFullAccess should cancel full access', () => {
    component.isDisabledBtn = false;
    component.isConfirmFullAccess = true;
    component.featureList = [
      { data: { features: { featureId: 1 }, isFullAccess: true } },
      { data: { features: { featureId: 2 }, isFullAccess: false } }
    ];
    component.selectedFeature = { features: { featureId: 1 } };
    component.OnCancelFullAccess(null);

    expect(component.isDisabledBtn).toBe(true);
    expect(component.featureList[0].data.isFullAccess).toBe(false);
    expect(component.selectedFeature).toBeNull();
    expect(component.isConfirmFullAccess).toBe(false);
  });

});