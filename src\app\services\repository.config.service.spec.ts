import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { RepositoryConfigService } from './repository.config.service';
import { HttpErrorResponse, HTTP_INTERCEPTORS } from '@angular/common/http';
import { OidcAuthService } from './oidc-auth.service';
import { AccountService } from './account.service';

describe('RepositoryConfigService', () => {
  let service: RepositoryConfigService;
  let httpMock: HttpTestingController;
  const mockBaseUrl = 'http://test-api/';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        RepositoryConfigService,
        { provide: 'BASE_URL', useValue: mockBaseUrl },
        { provide: OidcAuthService, useValue: { getToken: jasmine.createSpy('getToken').and.returnValue('mock-token') } },
        { provide: AccountService, useValue: { getToken: jasmine.createSpy('getToken').and.returnValue('mock-token'), redirectToUnauthorized: jasmine.createSpy('redirectToUnauthorized'), addupdateSessionId: jasmine.createSpy('addupdateSessionId') } },
        { provide: HTTP_INTERCEPTORS, useValue: { intercept: (req: any, next: any) => next.handle(req) }, multi: true }
      ]
    });
    service = TestBed.inject(RepositoryConfigService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service.myAppUrl).toBe(mockBaseUrl);
  });

  describe('errorHandler', () => {
    it('should return an Observable that throws the error', (done) => {
      const testError = new Error('Test error');
      
      service.errorHandler(testError).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: (error) => {
          expect(error).toBe(testError);
          done();
        }
      });
    });

    it('should handle HttpErrorResponse correctly', (done) => {
      const testHttpError = new HttpErrorResponse({
        error: 'Server error',
        status: 500,
        statusText: 'Internal Server Error'
      });
      
      service.errorHandler(testHttpError).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: (error) => {
          expect(error).toBe(testHttpError);
          expect(error.status).toBe(500);
          expect(error.error).toBe('Server error');
          done();
        }
      });
    });
  });

  describe('getRepositoryStructureData', () => {
    it('should get repository structure data for valid company and featureId', () => {
      const encryptedCompanyId = 'enc123';
      const featureId = 42;
      const mockResponse = { structure: [{ id: 1, name: 'Folder' }] };

      service.getRepositoryStructureData(encryptedCompanyId, featureId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-repository-structure/${encryptedCompanyId}/${featureId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle empty response for repository structure', () => {
      const encryptedCompanyId = 'enc123';
      const featureId = 42;
      const mockResponse = {};

      service.getRepositoryStructureData(encryptedCompanyId, featureId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-repository-structure/${encryptedCompanyId}/${featureId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle malformed response for repository structure', () => {
      const encryptedCompanyId = 'enc123';
      const featureId = 42;
      const mockResponse = 'not-an-object';

      service.getRepositoryStructureData(encryptedCompanyId, featureId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-repository-structure/${encryptedCompanyId}/${featureId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when getting repository structure data', (done) => {
      const encryptedCompanyId = 'enc123';
      const featureId = 42;

      service.getRepositoryStructureData(encryptedCompanyId, featureId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(404);
          expect(error.error).toBe('Not found');
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-repository-structure/${encryptedCompanyId}/${featureId}`);
      expect(req.request.method).toBe('GET');
      req.flush('Not found', { status: 404, statusText: 'Not Found' });
    });

    it('should handle network errors when getting repository structure data', (done) => {
      const encryptedCompanyId = 'enc123';
      const featureId = 42;

      service.getRepositoryStructureData(encryptedCompanyId, featureId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-repository-structure/${encryptedCompanyId}/${featureId}`);
      expect(req.request.method).toBe('GET');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });

    it('should handle invalid input (empty encryptedCompanyId)', (done) => {
      const encryptedCompanyId = '';
      const featureId = 42;

      service.getRepositoryStructureData(encryptedCompanyId, featureId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(400);
          expect(error.error).toBe('Invalid company id');
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-repository-structure//${featureId}`);
      expect(req.request.method).toBe('GET');
      req.flush('Invalid company id', { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('getRepositoryConfigs', () => {
    it('should get data collections for a given id', () => {
      const testId = 123;
      const mockResponse = { config: 'test-config' };

      service.getRepositoryConfigs(testId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-document-config/${testId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when getting data collections', (done) => {
      const testId = 123;
      
      service.getRepositoryConfigs(testId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(500);
          expect(error.error).toBe('Server error');
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/get-document-config/${testId}`);
      expect(req.request.method).toBe('GET');
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });
  });

  describe('updateRepositoryConfig', () => {
    it('should update data collection', () => {
      const mockConfig = { id: 123, name: 'Test Config' };
      const mockResponse = { success: true };

      service.updateRepositoryConfig(mockConfig).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/update-document-config`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockConfig);
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when updating data collection', (done) => {
      const mockConfig = { id: 123, name: 'Test Config' };
      const errorResponse = new HttpErrorResponse({
        error: 'Validation error',
        status: 400,
        statusText: 'Bad Request'
      });
      
      service.updateRepositoryConfig(mockConfig).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error.status).toBe(400);
          expect(error.error).toBe('Validation error');
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/update-document-config`);
      expect(req.request.method).toBe('POST');
      req.flush('Validation error', { status: 400, statusText: 'Bad Request' });
    });

    it('should handle network errors when updating data collection', (done) => {
      const mockConfig = { id: 123, name: 'Test Config' };
      
      service.updateRepositoryConfig(mockConfig).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/update-document-config`);
      expect(req.request.method).toBe('POST');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });  

  describe('getRepositoryConfigsForMultipleCompanies', () => {
    it('should get repository configs for multiple companies', () => {
      const featureId = 101;
      const companyIds = [1, 2, 3];
      const mockResponse = { configs: ['config1', 'config2'] };

      service.getRepositoryConfigsForMultipleCompanies(featureId, companyIds).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-combined-document-config/${featureId}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(companyIds);
      req.flush(mockResponse);
    });

    it('should handle network errors when getting repository configs for multiple companies', (done) => {
      const featureId = 101;
      const companyIds = [1, 2, 3];

      service.getRepositoryConfigsForMultipleCompanies(featureId, companyIds).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-combined-document-config/${featureId}`);
      expect(req.request.method).toBe('POST');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('deleteDocuments', () => {
    it('should delete documents for a given company ID', () => {
      const encryptedCompanyId = 'abc123';
      const formData = new FormData();
      formData.append('Path', '/documents/folder');
      formData.append('DocumentIds', '1');
      formData.append('DocumentIds', '2');
      formData.append('ModifiedBy', '0');
      
      const mockResponse = { isSuccess: true, message: 'Documents deleted successfully' };

      service.deleteDocuments(encryptedCompanyId, formData).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/delete-documents/${encryptedCompanyId}`);
      expect(req.request.method).toBe('POST');
      // We can't directly test FormData content because it's not easily accessible
      // But we can verify the URL and method
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when deleting documents', (done) => {
      const encryptedCompanyId = 'abc123';
      const formData = new FormData();
      formData.append('Path', '/documents/folder');
      formData.append('DocumentIds', '1');
      
      service.deleteDocuments(encryptedCompanyId, formData).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(400);
          expect(error.error).toBe('Invalid request');
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/delete-documents/${encryptedCompanyId}`);
      expect(req.request.method).toBe('POST');
      req.flush('Invalid request', { status: 400, statusText: 'Bad Request' });
    });

    it('should handle network errors when deleting documents', (done) => {
      const encryptedCompanyId = 'abc123';
      const formData = new FormData();
      formData.append('Path', '/documents/folder');
      formData.append('DocumentIds', '1');
      
      service.deleteDocuments(encryptedCompanyId, formData).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/delete-documents/${encryptedCompanyId}`);
      expect(req.request.method).toBe('POST');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('getCategoriesAndDocumentTypes', () => {
    it('should get categories and document types for a given company ID', () => {
      const companyId = 123;
      const mockResponse = { 
        categories: [
          { categoryId: 1, category: 'Category 1' },
          { categoryId: 2, category: 'Category 2' }
        ],
        documentTypes: [
          { documentTypeID: 1, documentName: 'Document Type 1' },
          { documentTypeID: 2, documentName: 'Document Type 2' }
        ]
      };

      service.getCategoriesAndDocumentTypes(companyId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/categories-and-doctypes/${companyId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when getting categories and document types', (done) => {
      const companyId = 123;
      
      service.getCategoriesAndDocumentTypes(companyId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(500);
          expect(error.error).toBe('Server error');
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/categories-and-doctypes/${companyId}`);
      expect(req.request.method).toBe('GET');
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('should handle network errors when getting categories and document types', (done) => {
      const companyId = 123;
      
      service.getCategoriesAndDocumentTypes(companyId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/categories-and-doctypes/${companyId}`);
      expect(req.request.method).toBe('GET');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('createUserInformation', () => {
    it('should create a new user information record', () => {
      const userInfoData = {
        entityId: 123,
        name: 'John Doe',
        email: '<EMAIL>',
        categoryId: 1,
        recipient: 'To',
        documentTypeIds: [1, 2]
      };
      
      const mockResponse = { 
        success: true,
        userInformationId: 456,
        message: 'User information created successfully'
      };

      service.createUserInformation(userInfoData).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/create-user`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(userInfoData);
      req.flush(mockResponse);
    });

    it('should handle validation errors when creating user information', (done) => {
      const userInfoData = {
        entityId: 123,
        name: 'John Doe',
        email: 'invalid-email', // Invalid email format
        categoryId: 1,
        recipient: 'To',
        documentTypeIds: [1, 2]
      };
      
      service.createUserInformation(userInfoData).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(400);
          expect(error.error).toBe('Validation failed: Invalid email format');
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/create-user`);
      expect(req.request.method).toBe('POST');
      req.flush('Validation failed: Invalid email format', { status: 400, statusText: 'Bad Request' });
    });

    it('should handle network errors when creating user information', (done) => {
      const userInfoData = {
        entityId: 123,
        name: 'John Doe',
        email: '<EMAIL>',
        categoryId: 1,
        recipient: 'To',
        documentTypeIds: [1, 2]
      };
      
      service.createUserInformation(userInfoData).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/create-user`);
      expect(req.request.method).toBe('POST');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('getUserInformationByCompany', () => {
    it('should get user information for a given company ID', () => {
      const companyId = 123;
      const mockResponse = [
        {
          userInformationID: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          category: 'Category 1',
          recipient: 'To',
          documentTypes: [
            { documentTypeID: 1, documentName: 'Document Type 1' },
            { documentTypeID: 2, documentName: 'Document Type 2' }
          ]
        },
        {
          userInformationID: 2,
          name: 'Jane Smith',
          email: '<EMAIL>',
          category: 'Category 2',
          recipient: 'CC',
          documentTypes: [
            { documentTypeID: 3, documentName: 'Document Type 3' }
          ]
        }
      ];

      service.getUserInformationByCompany(companyId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/user-info-by-company/${companyId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle empty response when getting user information', () => {
      const companyId = 123;
      const mockResponse = []; // Empty array response

      service.getUserInformationByCompany(companyId).subscribe(response => {
        expect(response).toEqual([]);
        expect(response.length).toBe(0);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/user-info-by-company/${companyId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when getting user information', (done) => {
      const companyId = 123;
      
      service.getUserInformationByCompany(companyId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(404);
          expect(error.error).toBe('Company not found');
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/user-info-by-company/${companyId}`);
      expect(req.request.method).toBe('GET');
      req.flush('Company not found', { status: 404, statusText: 'Not Found' });
    });

    it('should handle network errors when getting user information', (done) => {
      const companyId = 123;
      
      service.getUserInformationByCompany(companyId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/user-info-by-company/${companyId}`);
      expect(req.request.method).toBe('GET');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('getUserInformationById', () => {
    it('should get user information by ID', () => {
      const userInfoId = 456;
      const mockResponse = {
        userInformationID: 456,
        name: 'John Doe',
        email: '<EMAIL>',
        category: 'Category 1',
        recipient: 'To',
        documentTypes: [
          { documentTypeID: 1, documentName: 'Document Type 1' },
          { documentTypeID: 2, documentName: 'Document Type 2' }
        ]
      };

      service.getUserInformationById(userInfoId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/user-info/${userInfoId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when getting user information by ID', (done) => {
      const userInfoId = 456;
      
      service.getUserInformationById(userInfoId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(404);
          expect(error.error).toBe('User information not found');
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/user-info/${userInfoId}`);
      expect(req.request.method).toBe('GET');
      req.flush('User information not found', { status: 404, statusText: 'Not Found' });
    });

    it('should handle network errors when getting user information by ID', (done) => {
      const userInfoId = 456;
      
      service.getUserInformationById(userInfoId).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/user-info/${userInfoId}`);
      expect(req.request.method).toBe('GET');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('updateUserInformation', () => {
    it('should update an existing user information record', () => {
      const updateData = {
        userInformationID: 456,
        entityId: 123,
        name: 'John Doe Updated',
        email: '<EMAIL>',
        categoryId: 2,
        recipient: 'CC',
        documentTypeIds: [1, 3, 4]
      };
      
      const mockResponse = {
        success: true,
        message: 'User information updated successfully'
      };

      service.updateUserInformation(updateData).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/update-user`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockResponse);
    });

    it('should handle validation errors when updating user information', (done) => {
      const updateData = {
        userInformationID: 456,
        entityId: 123,
        name: 'John Doe Updated',
        email: 'invalid-email', // Invalid email format
        categoryId: 2,
        recipient: 'CC',
        documentTypeIds: [1, 3, 4]
      };
      
      service.updateUserInformation(updateData).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(400);
          expect(error.error).toBe('Validation failed: Invalid email format');
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/update-user`);
      expect(req.request.method).toBe('PUT');
      req.flush('Validation failed: Invalid email format', { status: 400, statusText: 'Bad Request' });
    });

    it('should handle not found error when updating non-existing user information', (done) => {
      const updateData = {
        userInformationID: 999, // Non-existing ID
        entityId: 123,
        name: 'John Doe Updated',
        email: '<EMAIL>',
        categoryId: 2,
        recipient: 'CC',
        documentTypeIds: [1, 3, 4]
      };
      
      service.updateUserInformation(updateData).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.status).toBe(404);
          expect(error.error).toBe('User information not found');
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/update-user`);
      expect(req.request.method).toBe('PUT');
      req.flush('User information not found', { status: 404, statusText: 'Not Found' });
    });

    it('should handle network errors when updating user information', (done) => {
      const updateData = {
        userInformationID: 456,
        entityId: 123,
        name: 'John Doe Updated',
        email: '<EMAIL>',
        categoryId: 2,
        recipient: 'CC',
        documentTypeIds: [1, 3, 4]
      };
      
      service.updateUserInformation(updateData).subscribe({
        next: () => {
          fail('Expected an error, not success');
          done();
        },
        error: error => {
          expect(error instanceof HttpErrorResponse).toBeTruthy();
          expect(error.error instanceof ErrorEvent).toBeTruthy();
          done();
        }
      });
      
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/update-user`);
      expect(req.request.method).toBe('PUT');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('getFunds', () => {
    it('should get the list of funds', () => {
      const mockResponse = [
        { fundID: 1, fundName: 'Fund 1' },
        { fundID: 2, fundName: 'Fund 2' }
      ];

      service.getFunds().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/get-fund-list`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toBeNull();
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when getting funds', (done) => {
      service.getFunds().subscribe({
        next: () => done.fail('should have failed with 500 error'),
        error: error => {
          expect(error).toBeTruthy();
          done();
        }
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/get-fund-list`);
      expect(req.request.method).toBe('POST');
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('should handle network errors when getting funds', (done) => {
      service.getFunds().subscribe({
        next: () => done.fail('should have failed with network error'),
        error: error => {
          expect(error).toBeTruthy();
          done();
        }
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/get-fund-list`);
      expect(req.request.method).toBe('POST');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

  describe('deleteUserInformation', () => {
    it('should delete a user information record by ID', () => {
      const userInformationId = 123;
      const mockResponse = { success: true, message: 'User deleted successfully' };

      service.deleteUserInformation(userInformationId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/delete-user/${userInformationId}`);
      expect(req.request.method).toBe('DELETE');
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when deleting user information', (done) => {
      const userInformationId = 456;
      service.deleteUserInformation(userInformationId).subscribe({
        next: () => done.fail('should have failed with 500 error'),
        error: error => {
          expect(error).toBeTruthy();
          expect(error.status).toBe(500);
          expect(error.error).toBe('Server error');
          done();
        }
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/delete-user/${userInformationId}`);
      expect(req.request.method).toBe('DELETE');
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('should handle network errors when deleting user information', (done) => {
      const userInformationId = 789;
      service.deleteUserInformation(userInformationId).subscribe({
        next: () => done.fail('should have failed with network error'),
        error: error => {
          expect(error).toBeTruthy();
          expect(error instanceof HttpErrorResponse).toBeTrue();
          expect(error.error instanceof ErrorEvent).toBeTrue();
          done();
        }
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/user-information/delete-user/${userInformationId}`);
      expect(req.request.method).toBe('DELETE');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

    describe('checkDuplicateName', () => {
    it('should return isDuplicateName true for duplicate group name', () => {
      const emailGroup = { groupName: 'Test Group', groupId: 0 };
      const mockResponse = { isDuplicateName: true };

      service.checkDuplicateName(emailGroup).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/email-groups/check-duplicate-name`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(emailGroup);
      req.flush(mockResponse);
    });

    it('should return isDuplicateName false for unique group name', () => {
      const emailGroup = { groupName: 'Unique Group', groupId: 0 };
      const mockResponse = { isDuplicateName: false };

      service.checkDuplicateName(emailGroup).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/email-groups/check-duplicate-name`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(emailGroup);
      req.flush(mockResponse);
    });

    it('should handle HTTP errors when checking duplicate name', (done) => {
      const emailGroup = { groupName: 'Error Group', groupId: 0 };

      service.checkDuplicateName(emailGroup).subscribe({
        next: () => done.fail('should have failed with error'),
        error: error => {
          expect(error).toBeTruthy();
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/email-groups/check-duplicate-name`);
      expect(req.request.method).toBe('POST');
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('should handle network errors when checking duplicate name', (done) => {
      const emailGroup = { groupName: 'Network Group', groupId: 0 };

      service.checkDuplicateName(emailGroup).subscribe({
        next: () => done.fail('should have failed with network error'),
        error: error => {
          expect(error).toBeTruthy();
          done();
        }
      });

      const req = httpMock.expectOne(`${mockBaseUrl}api/email-groups/check-duplicate-name`);
      expect(req.request.method).toBe('POST');
      const mockError = new ErrorEvent('Network error');
      req.error(mockError);
    });
  });

    describe('setResetInProgress', () => {
    it('should update resetInProgress$ observable', (done) => {
      service.setResetInProgress(true);
      service.resetInProgress$.subscribe(value => {
        expect(value).toBe(true);
        done();
      });
    });
  });

    describe('getPortfolioCompanies', () => {
    it('should get portfolio companies', () => {
      const mockResponse = [{ id: 1, name: 'Company' }];
      service.getPortfolioCompanies().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/get-portfolio-companies`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('should handle HTTP errors', (done) => {
      service.getPortfolioCompanies().subscribe({
        next: () => {},
        error: error => {
          expect(error).toBeTruthy();
          done();
        }
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/get-portfolio-companies`);
      req.flush('Error', { status: 500, statusText: 'Server Error' });
    });
  });

    describe('uploadDocumentstoServer', () => {
    it('should upload documents', () => {
      const formData = new FormData();
      const encId = 'encId';
      const mockResponse = { success: true };
      service.uploadDocumentstoServer(encId, formData).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/upload-documents/${encId}`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('downloadDocument', () => {
    it('should download document as blob', () => {
      const encId = 'encId', docId = 'docId';
      const mockBlob = new Blob(['test'], { type: 'application/pdf' });
      service.downloadDocument(encId, docId).subscribe(response => {
        expect(response).toBeInstanceOf(Blob);
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/download-document/${encId}/${docId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBlob);
    });

    it('should handle folderPath param', () => {
      const encId = 'encId', docId = 'docId', folderPath = '/folder';
      service.downloadDocument(encId, docId, folderPath).subscribe();
      const req = httpMock.expectOne(
        `${mockBaseUrl}api/download-document/${encId}/${docId}?folderPath=%2Ffolder`
      );
      expect(req.request.method).toBe('GET');
      req.flush(new Blob());
    });
  });

    describe('getRepositoryConfigs', () => {
    it('should handle null id', (done) => {
      service.getRepositoryConfigs(null as any).subscribe({
        next: () => {},
        error: error => {
          expect(error).toBeTruthy();
          done();
        }
      });
      const req = httpMock.expectOne(`${mockBaseUrl}api/get-document-config/null`);
      req.flush('Invalid id', { status: 400, statusText: 'Bad Request' });
    });
  });
});
