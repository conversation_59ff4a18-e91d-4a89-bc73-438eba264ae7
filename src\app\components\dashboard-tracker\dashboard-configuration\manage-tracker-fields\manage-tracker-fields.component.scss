@import '../../../../../variables';

// Commonly used pixel values
$px-0: 0px;
$px-2: 2px;
$px-4: 4px;
$px-6: 6px;
$px-24: 24px;
$px-68: 68px;

$svg-height: 68px;

.header-actions {
    display: flex;
}

.header-container {
    border-bottom: 1px solid $Neutral-Gray-10;
}

.no-data-svg {
    height: $svg-height;
}

.text-neutral-gray-80 {
    color: $Neutral-Gray-80;
}

.text-neutral-gray-90 {
    color: $Neutral-Gray-90;
}


.manage-tracker .from-tracking-period {
    border-top-right-radius: $px-0 !important;
    border-bottom-right-radius: $px-0 !important;
    border-right: $px-0 !important;
}


.manage-tracker .to-tracking-period {
    border-top-left-radius: $px-0 !important;
    border-bottom-left-radius: $px-0 !important;
    border: $border-neutral-grey !important;
}


.manage-tracker .prefix-suffix-dropdown {
    border-top-right-radius: $px-0 !important;
    border-bottom-right-radius: $px-0 !important;
    border: $border-neutral-grey !important;
}


.manage-tracker .month-year-dropdown {
    border-radius: $px-0 !important;
    border-left: $px-0 !important;
    border-right: $px-0 !important;
}


.manage-tracker .name-pattern-textbox {
    border-top-left-radius: $px-0 !important;
    border-bottom-left-radius: $px-0 !important;
}


.manage-tracker-tooltip {
    width: 232px !important;
    font-size: $font-size-medium !important;
}

.border-radius-2 {
    border-radius: 0.25rem !important;
    border: 1px solid $Neutral-Gray-10 !important;
}

.email-input-container {
    position: relative;
    margin-bottom: 0.5rem;

    .input-with-chips {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
        border: 1px solid $Neutral-Gray-10;
        border-left: none;
        border-radius: 0 0.25rem 0.25rem 0;

        input.form-control {
            flex: 1 1 200px;
            border: none;
            background: transparent;
            min-width: 150px;
            padding: 0.25rem 0.5rem;
        }
    }
}

.selection-chiptocc {
  padding: 0.35em 0.65em;
  border-radius: 1.25rem;
  background-color: $Neutral-Gray-05 !important;
  margin: $px-4;
}

.selection-chipicon{
    padding: 0.35em 0.65em;
    border-radius: 1.25rem;    
    margin: $px-4;
}

.remove-icon {
  cursor: pointer;
  &:hover {
    cursor: pointer;
  }
}

.reminder-input {
    height: 2rem;
}

.add-chip-btn {
    position: absolute;
    right: $px-6;
    background: #fff;
    border-radius: $px-4;
    width: $px-24;
    height: $px-24;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: border-color 0.2s;
    z-index: 2;
}

.add-icon{
    height: $px-24;
    width: $px-24;
}

.dropdown-options-container{
    flex: 0 0 120px;
    max-width: 120px;
    min-width: 90px;
}