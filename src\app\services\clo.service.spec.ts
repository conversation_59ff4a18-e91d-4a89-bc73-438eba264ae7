import { TestBed } from '@angular/core/testing';
import { CloService } from './clo.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('CloService', () => {
  let service: CloService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: 'BASE_URL', useValue: 'http://localhost:4200/' }
      ]
    });
    service = TestBed.inject(CloService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have a default value of null for currentData', (done: DoneFn) => {
    service.currentData.subscribe(data => {
      expect(data).toBeNull();
      done();
    });
  });

  it('should change the data when changeData is called', (done: DoneFn) => {
    const newData = { name: 'Test Data' };
    service.changeData(newData);

    service.currentData.subscribe(data => {
      expect(data).toEqual(newData);
      done();
    });
  });

  it('should emit new values when changeData is called multiple times', (done: DoneFn) => {
    const firstData = { name: 'First Data' };
    const secondData = { name: 'Second Data' };

    service.changeData(firstData);
    service.changeData(secondData);

    service.currentData.subscribe(data => {
      expect(data).toEqual(secondData);
      done();
    });
  });
});