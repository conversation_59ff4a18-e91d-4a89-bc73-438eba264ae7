<div class="container-fluid manage-tracker">
  <div class="row align-items-center mt-3 header-container">
    <div class="col">
      <span class="Body-M text-neutral-gray-90">Add New Column</span>
    </div>
    <div class="col-auto ms-auto header-actions mt-2 mb-2">
      <app-kendo-button
        [passedClass]="'width-94'"
        name="cancel"
        type="Secondary"
        (click)="resetForm()"
        >Cancel</app-kendo-button
      >
      <app-kendo-button
        [passedClass]="'width-94 mx-3'"
        name="save"
        type="Primary"
        [disabled]="!isCreateEnabled()"
        (click)="saveTrackerField()"
        >Save</app-kendo-button
      >
    </div>
  </div>
  <div class="row align-items-start">
    <!-- field type dropdown -->
    <div class="mb-4 mt-4" [ngClass]="isCreateEnabled() ? 'col-4' : 'col-6'">
      <div class="mb-2 Caption-M text-neutral-gray-80">Field Type</div>
      <kendo-combobox
        id="categoriesInput"
        class="k-custom-solid-dropdown k-dropdown-height-32"
        [data]="fieldTypesOptions"
        [clearButton]="false"
        [rounded]="'medium'"
        [fillMode]="'solid'"
        placeholder="Select Field Type"
        valueField="value"
        textField="text"
        [(ngModel)]="selectedFieldType"
      >
      </kendo-combobox>
    </div>
    <div class="col-6" *ngIf="!isCreateEnabled()">
      <div class="d-flex align-items-center bg-light mr-3 mt-3 mb-3 rounded">
        <div class="flex-grow-1 pl-3">
          <div class="Body-M">Please Select the Nature of Your Field Type.</div>
          <div class="Caption-R text-neutral-gray-80 mt-2">
            If you want to add static data, choose the Data option. If the data
            will be updated with each event, select the Time Series option.
          </div>
        </div>
        <img
          src="assets/dist/images/no-data-svg.svg"
          alt="info"
          class="no-data-svg"
        />
      </div>
    </div>
    <!-- frequency type dropdown -->       
    <div
      *ngIf="isCreateEnabled() && selectedFieldType.value === 2"
      class="col-4 mt-4"
    >
      <div class="mb-2 Caption-M text-neutral-gray-80">Tracking Frequency</div>
      <kendo-combobox
        id="trackingFrequencyInput"
        class="k-custom-solid-dropdown k-dropdown-height-32"
        [data]="trackingFrequencyOptions"
        [clearButton]="false"
        [rounded]="'medium'"
        [fillMode]="'solid'"
        placeholder="Select Data Type"
        valueField="value"
        textField="text"
        [(ngModel)]="selectedTrackingFrequency"
        (valueChange)="onChangeFrequency($event)"
      >
      </kendo-combobox>
    </div>
    <!-- period dropdown -->
    <div
      *ngIf="isCreateEnabled() && selectedFieldType.value === 2"
      class="col-4 mb-4 mt-4"
    >
      <div class="mb-2 d-flex align-items-center">
        <span class="Caption-M text-neutral-gray-80">Time Period</span>
        <img
          tooltipPosition="top"
          tooltipStyleClass="bg-tooltip-fund-color"
          [pTooltip]="'Click on file name for individual file download'"
          class="ml-1"
          src="assets/dist/images/info-icon.svg"
          alt="info-icon"
        />
      </div>
      <div class="row align-items-center m-0">
        <div class="col-6 p-0">
          <kendo-dropdowntree
            kendoDropDownTreeExpandable
            placeholder="From"
            [kendoDropDownTreeHierarchyBinding]="fromPeriodData"
            textField="text"
            valueField="id"
            childrenField="periods"
            [value]="selectedStartPeriodValue"
            (valueChange)="valueChangeStart($event)"
            class="from-tracking-period k-dropdown-height-32"
          >
            <ng-template kendoDropDownTreeValueTemplate let-dataItem>
              {{ (fromPeriodData.length > 0 && selectedStartPeriod) ? selectedStartPeriod : "From" }}
            </ng-template>
          </kendo-dropdowntree>
        </div>

        <div class="col-6 p-0">
          <kendo-dropdowntree
            kendoDropDownTreeExpandable
            placeholder="To"
            [kendoDropDownTreeHierarchyBinding]="toPeriodData"
            textField="text"
            valueField="id"
            childrenField="periods"
            [value]="selectedEndPeriodValue"
            (valueChange)="valueChangeEnd($event)"
            class="to-tracking-period k-dropdown-height-32"
          >
            <ng-template kendoDropDownTreeValueTemplate let-dataItem>
              {{ (toPeriodData.length > 0 && selectedEndPeriod) ? selectedEndPeriod : "To" }}
            </ng-template>
          </kendo-dropdowntree>
        </div>
      </div>
    </div>
   
    <!-- mapto dropdown item -->
    <div *ngIf="isCreateEnabled() && selectedFieldType.value === 1" class="col-4 mb-4 mt-4">
      <div class="mb-2 d-flex align-items-center Caption-M text-neutral-gray-80">Map To</div>
      <kendo-combobox
        id="maptoInput"
        class="k-custom-solid-dropdown k-dropdown-height-32"
        [data]="maptoFields"
        [clearButton]="false"
        [rounded]="'medium'"
        [fillMode]="'solid'"
        placeholder="Select Map To"
        valueField="value"
        textField="text"
        [(ngModel)]="selectedMaptoField"
      >
      </kendo-combobox>
    </div>
    <!-- Data type dropdown -->
     <div *ngIf="isCreateEnabled() && enableDataTypeDropdown()" class="col-4 mb-4 mt-4">
      <div class="mb-2 d-flex align-items-center Caption-M text-neutral-gray-80">Data Type</div>
      <kendo-combobox
        id="dataTypeInput"
        class="k-custom-solid-dropdown k-dropdown-height-32"
        [data]="dataTypesOptions"
        [clearButton]="false"
        [rounded]="'medium'"
        [fillMode]="'solid'"
        placeholder="Select Data Type"
        valueField="value"
        textField="text"
        [(ngModel)]="selectedDataType"
      >
      </kendo-combobox>
    </div>
     <!-- column name text box -->
    <div *ngIf="isCreateEnabled()" class="col-4 mb-4 mt-4">
      <div
        class="mb-2 d-flex align-items-center"        
      >
        <span class="Caption-M text-neutral-gray-80">Name Pattern</span>
        <img
          *ngIf="selectedFieldType.value !== 1"
          kendoTooltip
          [tooltipTemplate]="template"
          filter="img"
          class="ml-1"
          src="assets/dist/images/info-icon.svg"
          alt="info-icon"
        />
        <ng-template #template>
          <div class="Caption-R manage-tracker-tooltip">
            {{ namePatternTooltip }}
          </div>
        </ng-template>
      </div>
      <div class="row align-items-center">
        <div class="col-3 pr-0" *ngIf="selectedFieldType.value === 2">
          <kendo-combobox
            class="k-custom-solid-dropdown k-dropdown-height-32 me-2 prefix-suffix-dropdown"
            [data]="prefixSuffixOptions"
            [clearButton]="false"
            [rounded]="'medium'"
            [fillMode]="'solid'"
            valueField="value"
            textField="text"
            [(ngModel)]="selectedPrefixSuffix"
          >
          </kendo-combobox>
        </div>
        <div class="col-3 p-0" *ngIf="selectedFieldType.value === 2">
          <kendo-combobox
            class="k-custom-solid-dropdown k-dropdown-height-32 month-year-dropdown"
            [data]="monthYearOptions()"
            [clearButton]="false"
            [rounded]="'medium'"
            [fillMode]="'solid'"
            valueField="value"
            textField="text"
            [(ngModel)]="selectedTSDateFormat"            
          >
          </kendo-combobox>
        </div>
        <div [ngClass]="selectedFieldType.value === 1 ? 'col-12' : 'col-6 pl-0'">
          <kendo-textbox
            class="k-custom-solid-dropdown k-dropdown-height-32 name-pattern-textbox"
            placeholder="Enter Column Name"
            [(ngModel)]="columnName"
          >
          </kendo-textbox>
        </div>
      </div>
    </div>
    <!-- Dropdown Value Chips Input -->
    <div *ngIf="isCreateEnabled() && selectedDataType?.text === 'Dropdown'" class="col-12 mb-4">      
        <label class="mb-2 Caption-M text-neutral-gray-80">Dropdown Value</label>
        <div class="d-flex">
          <div *ngIf="selectedFieldType.text === 'Time Series'" class="dropdown-options-container">
            <kendo-combobox class="k-custom-solid-dropdown k-dropdown-height-34 me-2 prefix-suffix-dropdown"
              [data]="dropdownOptions" [clearButton]="false" [rounded]="'medium'" [fillMode]="'solid'" valueField="value"
              textField="text" [(ngModel)]="selectedDropdownOption">
            </kendo-combobox>
          </div>
          <div class="flex-grow-1" *ngIf="selectedDropdownOption.text === 'Icon' && selectedFieldType.text === 'Time Series'">
            <kendo-multiselect
              class="k-multiselect-custom k-dropdown-width-100 icon-multiselect"
              [data]="shareData"
              [(ngModel)]="selectedIcon"
              [rounded]="'medium'"
              [fillMode]="'solid'"
              [clearButton]="false"
              textField="text"
              valueField="text"
              [autoClose]="false"
              [checkboxes]="false"
              (valueChange)="onShareSelect($event)"
            >
              <ng-template kendoMultiSelectItemTemplate let-dataItem>
                <span class="icon-option d-flex align-items-center">
                  <kendo-svg-icon [icon]="dataItem.svgIcon" size="medium" class="mr-2"></kendo-svg-icon>
                  <span>{{ dataItem.text }}</span>
                </span>
              </ng-template>
              <ng-template kendoMultiSelectTagTemplate let-dataItem>
                <span class="selection-chipicon d-flex align-items-center">
                  <kendo-svg-icon [icon]="dataItem.svgIcon" size="small" class="mr-1"></kendo-svg-icon>
                  {{ dataItem.text }}
                </span>
              </ng-template>
            </kendo-multiselect>
          </div>
          <div class="flex-grow-1" *ngIf="selectedDropdownOption.text === 'Text'">
            <div class="email-input-container">
              <div class="input-with-chips" [ngClass]="{'border-radius-2': selectedFieldType.text === 'Data'}">
                <div class="chips-container-inline">
                  <span *ngFor="let value of dropdownValues; let i = index" class="selection-chiptocc Caption-R">
                    {{ value }}
                    <img [src]="'assets/dist/images/cross-blackicon.svg'" alt="remove" class="remove-icon"
                      (click)="removeDropdownValue(i)">
                  </span>
                </div>
                <input type="text" class="form-control reminder-input Caption-R" [(ngModel)]="dropdownInput"
                  (keydown.enter)="addDropdownValue($event)" id="dropdownInput" />
                <button type="button" class="add-chip-btn" (click)="addDropdownValue($event)">
                  <img [src]="'assets/dist/images/plus-icon-k.svg'" alt="add" class="add-icon">
                </button>
              </div>
          </div>          
        </div>          
    </div>
  </div>

    <div class="my-3 w-100 ml-3 mr-3">
      <ng-container>
          <app-manage-tracker-records [isNewColumnAdded]="isNewColumnAdded"></app-manage-tracker-records>
      </ng-container>
    </div>
</div>
