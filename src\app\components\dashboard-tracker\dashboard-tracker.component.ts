import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { DashboardTrackerService } from '../../services/dashboard-tracker.service';
import { Observable, of, Subject } from 'rxjs';
import { GridDataResult } from '@progress/kendo-angular-grid';
import { State } from '@progress/kendo-data-query';
import { DashboardCellValueDto, SaveDashboardCellValuesDto } from './model/dashboard-tracker-config.model';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-dashboard-tracker',
  templateUrl: './dashboard-tracker.component.html',
  styleUrls: ['./dashboard-tracker.component.scss']
})
export class DashboardTrackerComponent implements OnInit, OnDestroy {
  @Input() passedClass: string = '';
  @Input() isDashboardConfigurationTab: boolean = false;
  @Output() cellChangesUpdated = new EventEmitter<number>();
  isLoading: boolean = true;
  moreColumn: boolean = false;
  gridColumns: any[] = [];
  totalRecords: number = 0;
  defaultDateFormat: string = 'DD/MM/YYYY'; // placeholder text display only
  state: State = {
    skip: 0,
    take: 100
  };
  view: Observable<GridDataResult>;

  // Collection to store cell value changes
  cellValueChanges: DashboardCellValueDto[] = [];

  // Collection to store validation errors for textboxes
  validationErrors: Map<string, string> = new Map();

  // Subject for debouncing textbox changes
  private textboxChangeSubject = new Subject<{value: string, dataItem: any, column: any}>();
  private destroy$ = new Subject<void>();

  constructor(
    private dashboardTrackerService: DashboardTrackerService,
    private toastrService: ToastrService,
  ) {}

  ngOnInit(): void {
    this.loadDashboardTableData(this.state);
    this.setupTextboxDebounce();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupTextboxDebounce(): void {
    this.textboxChangeSubject
      .pipe(
        debounceTime(500), // 2 second debounce
        takeUntil(this.destroy$)
      )
      .subscribe(({value, dataItem, column}) => {
        this.updateCellValue(dataItem, column, value);
      });
  }

  loadDashboardTableData(state: State): void {
    const filter = {
      First: state.skip,
      Rows: state.take
    };
    this.isLoading = true;
    this.dashboardTrackerService.getDashboardTableData(filter).subscribe((response) => {
      if (response && response.data && response.columns) {
        this.gridColumns = response.columns;
        this.totalRecords = response.totalRecords || 0;
        this.view = of<GridDataResult>({
          data: response.data,
          total: this.totalRecords
        });        
      } else {
        this.view = of<GridDataResult>({ data: [], total: 0 });
      }
      this.isLoading = false;
    });
  }

  dataStateChange(event: any): void {
    this.state = event;
    this.loadDashboardTableData(this.state);
  }

  navigateToDashboardConfig(): void {

  }

  // Handle dropdown value changes
  onDropdownValueChange(value: string, dataItem: any, column: any): void {
    this.updateCellValue(dataItem, column, value);
  }

  // Handle textbox value changes with debounce
  onTextboxValueChange(value: string, dataItem: any, column: any): void {
    // Validate input based on data type
    const validationKey = this.getValidationKey(dataItem, column);
    const validationResult = this.validateInput(value, column.dataType);

    if (validationResult.isValid) {
      // Clear any existing validation error
      this.validationErrors.delete(validationKey);
      // Immediately update the UI for better user experience
      dataItem[column.name] = value;
      // Send to debounced subject for actual processing
      this.textboxChangeSubject.next({value, dataItem, column});
    } else {
      // Store validation error
      this.validationErrors.set(validationKey, validationResult.errorMessage);
      // Still update UI to show user input, but don't process for saving
      dataItem[column.name] = value;
    }
  }

  // Handle clear button click for textbox
  clearTextboxValue(dataItem: any, column: any): void {
    const validationKey = this.getValidationKey(dataItem, column);
    // Clear validation error
    this.validationErrors.delete(validationKey);
    // Clear the value and process normally
    this.onTextboxValueChange('', dataItem, column);
  }

  // Update or add cell value to the collection
  private updateCellValue(dataItem: any, column: any, newValue: any): void {
    const portfolioCompanyId = dataItem['PCID'];
    const fundId = dataItem['FundID'];
    const columnId = column.id;
    const timeSeriesId = column.timeSeriesID || null;

    // Validate required fields
    if (!portfolioCompanyId || !columnId) {
      this.toastrService.error("Missing required IDs for cell value update.", '', { positionClass: 'toast-center-center' });
      return;
    }

    // Check if there are any validation errors for this field
    const validationKey = this.getValidationKey(dataItem, column);
    if (this.validationErrors.has(validationKey)) {
      // Don't save invalid data
      return;
    }

    // Find existing entry or create new one
    const existingIndex = this.cellValueChanges.findIndex(
      item => item.PortfolioCompanyId === portfolioCompanyId &&
              item.FundId === fundId &&
              item.ColumnId === columnId &&
              item.TimeSeriesID === timeSeriesId
    );

    const cellValueDto: DashboardCellValueDto = {
      PortfolioCompanyId: portfolioCompanyId,
      FundId: fundId,
      ColumnId: columnId,
      TimeSeriesID: timeSeriesId,
      CellValue: newValue || null,
    };

    if (existingIndex >= 0) {
      // Update existing entry
      this.cellValueChanges[existingIndex] = cellValueDto;
    } else {
      // Add new entry
      this.cellValueChanges.push(cellValueDto);
    }

    // Update the grid data item for immediate UI feedback
    dataItem[column.name] = newValue;

    // Emit change event to parent component
    this.cellChangesUpdated.emit(this.cellValueChanges.length);
  }

  // Save all cell value changes
  saveCellValues(): void {
    if (this.cellValueChanges.length === 0) {
      return;
    }

    // Check if there are any validation errors
    if (this.validationErrors.size > 0) {
      this.toastrService.error('Please fix validation errors before saving.', '', { positionClass: 'toast-center-center' });
      return;
    }

    const payload: SaveDashboardCellValuesDto = {
      CellValues: this.cellValueChanges
    };

    this.dashboardTrackerService.saveDashboardCellValues(payload).subscribe({
      next: (response) => {
        this.toastrService.success('Modified values saved successfully.', '', { positionClass: 'toast-center-center' });
        // Clear the changes collection after successful save
        this.cellValueChanges = [];
        // Emit change event to parent component
        this.cellChangesUpdated.emit(this.cellValueChanges.length);
      },
      error: (error) => {
        this.toastrService.error('Failed to save cell values.', '', { positionClass: 'toast-center-center' });
      }
    });
  }

  // Get pending changes count for UI display
  getPendingChangesCount(): number {
    return this.cellValueChanges.length;
  }

  // Clear all pending changes
  clearPendingChanges(): void {
    this.cellValueChanges = [];
    this.validationErrors.clear();
    // Emit change event to parent component
    this.cellChangesUpdated.emit(this.cellValueChanges.length);
  }

  // Get current changes for debugging
  getCurrentChanges(): DashboardCellValueDto[] {
    return [...this.cellValueChanges];
  }

  // Generate unique key for validation tracking
  private getValidationKey(dataItem: any, column: any): string {
    const portfolioCompanyId = dataItem['PCID'];
    const columnId = column.id;
    return `${portfolioCompanyId}_${columnId}`;
  }

  // Validate input based on data type
  private validateInput(value: string, dataType: number): {isValid: boolean, errorMessage: string} {
    // If empty value, consider it valid (user can clear the field)
    if (!value || value.trim() === '') {
      return { isValid: true, errorMessage: '' };
    }

    switch (dataType) {
      case 2: // Number
        return this.validateNumber(value);
      case 3: // Date
        return this.validateDate(value);
      default:
        return { isValid: true, errorMessage: '' };
    }
  }

  // Validate number input
  private validateNumber(value: string): {isValid: boolean, errorMessage: string} {
    const trimmedValue = value.trim();

    // Check if it's a valid number
    if (isNaN(Number(trimmedValue)) || trimmedValue === '') {
      return { isValid: false, errorMessage: 'Please enter a valid number' };
    }

    // Additional checks for number format
    const numberRegex = /^-?\d*\.?\d+$/;
    if (!numberRegex.test(trimmedValue)) {
      return { isValid: false, errorMessage: 'Please enter a valid number format' };
    }

    return { isValid: true, errorMessage: '' };
  }

  // Validate date input
  private validateDate(value: string): {isValid: boolean, errorMessage: string} {
    const trimmedValue = value.trim();

    // Check DD/MM/YYYY format
    const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
    const match = trimmedValue.match(dateRegex);

    if (!match) {
      return { isValid: false, errorMessage: 'Please enter date in DD/MM/YYYY format' };
    }

    const day = parseInt(match[1], 10);
    const month = parseInt(match[2], 10);
    const year = parseInt(match[3], 10);

    // Basic date validation
    if (month < 1 || month > 12) {
      return { isValid: false, errorMessage: 'Invalid month (01-12)' };
    }

    if (day < 1 || day > 31) {
      return { isValid: false, errorMessage: 'Invalid day (01-31)' };
    }

    // More precise date validation
    const date = new Date(year, month - 1, day);
    if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
      return { isValid: false, errorMessage: 'Invalid date' };
    }

    return { isValid: true, errorMessage: '' };
  }

  public hasErrors(): boolean {
    return this.validationErrors.size > 0;
  }
  // Check if a specific textbox has validation error
  hasValidationError(dataItem: any, column: any): boolean {
    const validationKey = this.getValidationKey(dataItem, column);
    return this.validationErrors.has(validationKey);
  }

  // Get validation error message for a specific textbox
  getValidationError(dataItem: any, column: any): string {
    const validationKey = this.getValidationKey(dataItem, column);
    return this.validationErrors.get(validationKey) || '';
  }

  getDropDownValuesData(value: any, dropDownValues: any[]): string {
    const selectedValue = dropDownValues.find(item => item.value === value);
    return selectedValue ? selectedValue.displayText : '';
  }
}
