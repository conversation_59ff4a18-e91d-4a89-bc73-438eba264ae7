import { TestBed } from '@angular/core/testing';
import { CookieService } from './cookie.service';

describe('CookieService', () => {
  let service: CookieService;
  let mockCookieStore: string;

  beforeEach(() => {
    TestBed.configureTestingModule({ providers: [CookieService] });
    service = TestBed.get(CookieService);

    // Mock document.cookie behavior
    mockCookieStore = '';

    // Create a spy for document.cookie getter/setter
    Object.defineProperty(document, 'cookie', {
      get: () => mockCookieStore,
      set: (value: string) => {
        // Parse the cookie string and add it to our mock store
        const [cookiePart] = value.split(';');
        const [name, val] = cookiePart.split('=');

        if (val === '' || value.includes('expires=')) {
          // Check if this is a deletion (empty value or past expiry date)
          const isExpired = value.includes('expires=') &&
            value.includes('expires=' + new Date(Date.now() - 86400000).toUTCString().substring(0, 10));

          if (val === '' || isExpired) {
            // Handle cookie deletion or expiry
            const cookieRegex = new RegExp(`(^|;)\\s*${name}\\s*=\\s*[^;]*`, 'g');
            mockCookieStore = mockCookieStore.replace(cookieRegex, '');
            mockCookieStore = mockCookieStore.replace(/^;\s*/, '').replace(/;\s*;/g, ';');
          } else {
            // Add or update cookie (has expiry but not expired)
            const existingCookieRegex = new RegExp(`(^|;)\\s*${name}\\s*=\\s*[^;]*`);
            if (existingCookieRegex.test(mockCookieStore)) {
              mockCookieStore = mockCookieStore.replace(existingCookieRegex, `$1${name}=${val}`);
            } else {
              mockCookieStore = mockCookieStore ? `${mockCookieStore}; ${name}=${val}` : `${name}=${val}`;
            }
          }
        } else {
          // Add or update cookie
          const existingCookieRegex = new RegExp(`(^|;)\\s*${name}\\s*=\\s*[^;]*`);
          if (existingCookieRegex.test(mockCookieStore)) {
            mockCookieStore = mockCookieStore.replace(existingCookieRegex, `$1${name}=${val}`);
          } else {
            mockCookieStore = mockCookieStore ? `${mockCookieStore}; ${name}=${val}` : `${name}=${val}`;
          }
        }
      },
      configurable: true
    });
  });

  afterEach(() => {
    // Clean up the mock
    mockCookieStore = '';
  });

  it('can load instance', () => {
    expect(service).toBeTruthy();
  });

  it('should set cookie', () => {
    const key = 'testKey';
    const value = 'testValue';
    const expiry = new Date();
    expiry.setDate(expiry.getDate() + 1); // Set expiry to one day in the future

    service.setCookie(key, value, expiry);
    expect(document.cookie).toContain(`${key}=${value}`);
  });

  it('should get cookie', () => {
    const key = 'testKey';
    const value = 'testValue';

    // Set cookie directly in our mock store
    document.cookie = `${key}=${value}`;
    expect(service.getCookie(key)).toEqual(value);
  });

  it('should return null for non-existent cookie', () => {
    expect(service.getCookie('nonExistentKey')).toBeNull();
  });

  it('should remove cookie', () => {
    const key = 'testKey';
    const value = 'testValue';

    // First set a cookie
    document.cookie = `${key}=${value}`;
    expect(service.getCookie(key)).toEqual(value);

    // Then remove it
    service.removeCookie(key);
    expect(service.getCookie(key)).toBeNull();
  });
});
