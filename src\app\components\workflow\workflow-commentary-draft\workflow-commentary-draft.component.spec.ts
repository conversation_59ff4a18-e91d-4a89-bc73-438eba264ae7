import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { WorkflowCommentaryDraftComponent } from './workflow-commentary-draft.component';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { WorkflowCompanyService } from 'src/app/services/workflow-company.service';
import { DataService } from 'src/app/services/data-service.service';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { PermissionService, UserSubFeaturesEnum } from 'src/app/services/permission.service';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { of, throwError } from 'rxjs';
import { CompanyInformationConstants, WorkflowPcCommentary } from 'src/app/common/constants';
import { FormsModule } from '@angular/forms';
import { AccordionModule } from 'primeng/accordion';
import { HttpClientModule } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { AccountService } from 'src/app/services/account.service';
import { OidcAuthService } from 'src/app/services/oidc-auth.service';

/**
 * Test suite for WorkflowCommentaryDraftComponent.
 * Tests component initialization, commentary management, and workflow status updates.
 */
describe('WorkflowCommentaryDraftComponent', () => {
  let component: WorkflowCommentaryDraftComponent;
  let fixture: ComponentFixture<WorkflowCommentaryDraftComponent>;
  let workflowCompanyService: jasmine.SpyObj<WorkflowCompanyService>;
  let toastrService: jasmine.SpyObj<ToastrService>;

  const mockActivatedRoute = {
    snapshot: {
      params: { id: '123' }
    }
  };

  beforeEach(async () => {
    const workflowCompanyServiceSpy = jasmine.createSpyObj('WorkflowCompanyService', [
      'getPortfolioCompanyById',
      'getPortfolioCompanyCommentarySections',
      'saveCustomCommentaryDraft',
      'saveCompanyCommentaryDetails',
      'updateStatusCommentry',
      'getPortfolioCompanyCommentaryHistoricalSections',
      'updateWorkflowRequest',
      'addWorkflowComment',
      'getWorkflowComments'
    ]);
    
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error']);
    
    const accountServiceSpy = jasmine.createSpyObj('AccountService', ['getUserPermissions']);
    accountServiceSpy.getUserPermissions.and.returnValue(of([]));

    const oidcAuthServiceSpy = jasmine.createSpyObj('OidcAuthService', ['getAccessToken']);
    oidcAuthServiceSpy.getAccessToken.and.returnValue('mock-token');

    await TestBed.configureTestingModule({
      declarations: [ WorkflowCommentaryDraftComponent ],
      imports: [ FormsModule, AccordionModule, HttpClientModule ],
      providers: [
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: WorkflowCompanyService, useValue: workflowCompanyServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: 'BASE_URL', useValue: environment.apiBaseUrl },
        { provide: AccountService, useValue: accountServiceSpy },
        { provide: OidcAuthService, useValue: oidcAuthServiceSpy },
        DataService,
        PortfolioCompanyService,
        PermissionService,
        MiscellaneousService
      ]
    }).compileComponents();

    workflowCompanyService = TestBed.inject(WorkflowCompanyService) as jasmine.SpyObj<WorkflowCompanyService>;
    toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(WorkflowCommentaryDraftComponent);
    component = fixture.componentInstance;
    
    // Initialize with specific date for consistent testing
    const testDate = new Date(2025, 2, 15); // March 15, 2025
    jasmine.clock().mockDate(testDate);
    
    // Initialize required properties
    component.CustomCommentryValues = [];
    component.selectedCommentaryPeriod = { value: 'Monthly', name: 'Monthly' };
    component.commentaryCalendarValue = testDate;
    component.workFlowRequestId = 1;
    component.workflowMappingId = 1;
    component.id = '123';
    component.quarter = 'Q1';
    component.year = 2025;
    component.month = 3;

    // Setup default spy responses
    workflowCompanyService.getPortfolioCompanyCommentaryHistoricalSections.and.returnValue(of({
      result: {
        significantEventsSection: '',
        assessmentSection: '',
        exitPlansSection: '',
        impactSection: ''
      },
      customcommentary: []
    }));

    workflowCompanyService.getPortfolioCompanyCommentarySections.and.returnValue(of({
      result: null,
      customcommentary: []
    }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Tests for portfolio company draft retrieval functionality
   */
  describe('getDraftPortfolioCompanies', () => {
    beforeEach(() => {
      // Reset the spy call counts before each test
      workflowCompanyService.getPortfolioCompanyById.calls.reset();
      workflowCompanyService.getPortfolioCompanyCommentarySections.calls.reset();
    });

    it('should fetch portfolio company details successfully', fakeAsync(() => {
      const mockResponse = {
        commentaryPeriod: { chartValue: 'Monthly,Quarterly,Annual' },
        subPageList: [],
        fieldValueList: [],
        companyDetails: {}
      };

      workflowCompanyService.getPortfolioCompanyById.and.returnValue(of(mockResponse));
      workflowCompanyService.getPortfolioCompanyCommentarySections.and.returnValue(of({
        result: null,
        customcommentary: []
      }));

      component.getDraftPortfolioCompanies();
      tick();

      expect(workflowCompanyService.getPortfolioCompanyById).toHaveBeenCalled();
      expect(workflowCompanyService.getPortfolioCompanyCommentarySections).toHaveBeenCalledWith({
        encryptedPortfolioCompanyId: '123',
        quarter: '',
        year: 0,
        month: 0,
        period: 'Monthly',
        workflowRequestId: 1
      });
    }));

    it('should handle error when fetching portfolio company details', fakeAsync(() => {
      workflowCompanyService.getPortfolioCompanyById.and.returnValue(throwError(() => new Error('Error')));
      workflowCompanyService.getPortfolioCompanyCommentarySections.and.returnValue(of({
        result: null,
        customcommentary: []
      }));

      component.getDraftPortfolioCompanies();
      tick();

      expect(toastrService.error).toHaveBeenCalled();
      expect(component.loading).toBeFalse();
      // This service should not be called when getPortfolioCompanyById fails
      expect(workflowCompanyService.getPortfolioCompanyCommentarySections).not.toHaveBeenCalled();
    }));
  });

  /**
   * Tests for commentary saving functionality.
   * Covers both custom and standard commentary types.
   */
  describe('saveCommentry', () => {
    beforeEach(() => {
      // Set up common test data
      component.CustomCommentryValues = [];
      component.model = { portfolioCompanyID: 1 };
      workflowCompanyService.updateStatusCommentry.and.returnValue(of({}));
    });

    it('should save custom commentary successfully', fakeAsync(() => {
      const mockEvent = new MouseEvent('click');
      const mockCommentSection = {
        name: CompanyInformationConstants.Customfield,
        value: 'Test value',
        fieldID: 1
      };
      component.commentFieldList = [mockCommentSection];
      
      workflowCompanyService.saveCustomCommentaryDraft.and.returnValue(of({ message: 'Success' }));

      component.saveCommentry(mockEvent, 0);
      tick();

      expect(workflowCompanyService.saveCustomCommentaryDraft).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith(WorkflowPcCommentary.SuccessCommentary, '', jasmine.any(Object));
    }));

    it('should save standard commentary successfully', fakeAsync(() => {
      const mockEvent = new MouseEvent('click');
      const mockCommentSection = {
        name: CompanyInformationConstants.SignificantEvents,
        value: 'Test value',
        fieldID: 1
      };
      component.commentFieldList = [mockCommentSection];
      component.model = { portfolioCompanyID: 1 };
      
      workflowCompanyService.saveCompanyCommentaryDetails.and.returnValue(of({ message: 'Success' }));
      workflowCompanyService.updateStatusCommentry.and.returnValue(of({}));

      component.saveCommentry(mockEvent, 0);
      tick();

      expect(workflowCompanyService.saveCompanyCommentaryDetails).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith(WorkflowPcCommentary.SuccessCommentary, '', jasmine.any(Object));
    }));
  });

  /**
   * Tests for commentary period change handling.
   * Verifies correct calendar format and API calls for different period types.
   */
  describe('onChangeCommentaryPeriod', () => {
    beforeEach(() => {
      // Reset values before each test
      component.quarter = '';
      component.year = 0;
      component.month = 0;
    });

    it('should set correct calendar format for Monthly period', () => {
      component.selectedCommentaryPeriod = { value: 'Monthly', name: 'Monthly' };
      component.onChangeCommentaryPeriod();
      
      expect(component.commentaryCalendarFormat).toBe('MMMM yyyy');
      expect(component.commentaryCalendarActiveView).toBe('year');
      expect(component.showQuarterYearControl).toBeFalse();
      expect(workflowCompanyService.getPortfolioCompanyCommentarySections).toHaveBeenCalledWith({
        encryptedPortfolioCompanyId: '123',
        quarter: '',
        year: 0,
        month: 0,
        period: 'Monthly',
        workflowRequestId: 1
      });
    });

    it('should set correct settings for Quarterly period', () => {
      component.selectedCommentaryPeriod = { value: 'Quarterly', name: 'Quarterly' };
      component.onChangeCommentaryPeriod();
      
      expect(component.showQuarterYearControl).toBeTrue();
      expect(workflowCompanyService.getPortfolioCompanyCommentarySections).toHaveBeenCalledWith({
        encryptedPortfolioCompanyId: '123',
        quarter: '',
        year: 0,
        month: 0,
        period: 'Quarterly',
        workflowRequestId: 1
      });
    });

    it('should set correct calendar format for Annual period', () => {
      component.selectedCommentaryPeriod = { value: 'Annual', name: 'Annual' };
      component.onChangeCommentaryPeriod();
      
      expect(component.commentaryCalendarFormat).toBe('yyyy');
      expect(component.commentaryCalendarActiveView).toBe('decade');
      expect(component.showQuarterYearControl).toBeFalse();
      expect(workflowCompanyService.getPortfolioCompanyCommentarySections).toHaveBeenCalledWith({
        encryptedPortfolioCompanyId: '123',
        quarter: '',
        year: 0,
        month: 0,
        period: 'Annual',
        workflowRequestId: 1
      });
    });

    it('should fetch historical commentary when period changes', fakeAsync(() => {
      // Mock the service to return existing data so component doesn't use current date
      const mockResponse = {
        result: {
          quarter: 'Q1',
          year: 2025,
          month: 3
        },
        customcommentary: []
      };
      workflowCompanyService.getPortfolioCompanyCommentarySections.and.returnValue(of(mockResponse));

      component.selectedCommentaryPeriod = { value: 'Monthly', name: 'Monthly' };
      component.onChangeCommentaryPeriod();
      tick();

      expect(workflowCompanyService.getPortfolioCompanyCommentaryHistoricalSections).toHaveBeenCalledWith({
        encryptedPortfolioCompanyId: '123',
        quarter: 'Q1',
        year: 2025,
        month: 3,
        period: 'Monthly'
      });
    }));
  });

  /**
   * Tests for comment field list filtering functionality.
   * Verifies proper removal of commentary period fields.
   */
  describe('filterCommentFieldList', () => {
    it('should remove commentary period fields from the list', () => {
      const mockFields = [
        { name: 'regular field' },
        { name: component.commentaryPeriodConst },
        { name: 'another field' }
      ];
      component.commentFieldList = mockFields;      
      component.filterCommentFieldList();
      
      expect(component.commentFieldList.length).toBe(2);
      expect(component.commentFieldList.some(f => f.name === component.commentaryPeriodConst)).toBeFalse();
    });
  });

  /**
   * Tests for historical commentary retrieval and processing.
   * Verifies preservation of draft values and proper handling of historical data.
   */
  describe('getPortfolioCompaniesHistoricalCommemtary', () => {
    it('should fetch and process historical commentary data', fakeAsync(() => {
      const mockHistoricalData = {
        result: {
          significantEventsSection: 'historical event',
          assessmentSection: 'historical assessment',
          exitPlansSection: 'historical exit plan',
          impactSection: 'historical impact'
        },
        customcommentary: [{
          fieldID: 1,
          value: 'historical custom value'
        }]
      };

      workflowCompanyService.getPortfolioCompanyCommentaryHistoricalSections.and.returnValue(of(mockHistoricalData));

      component.commentFieldList = [
        { fieldID: 1, name: CompanyInformationConstants.Customfield, value: 'draft value' },
        { fieldID: 2, name: CompanyInformationConstants.SignificantEvents }
      ];

      component.getPortfolioCompaniesHistoricalCommemtary();
      tick();

      expect(workflowCompanyService.getPortfolioCompanyCommentaryHistoricalSections).toHaveBeenCalled();
      expect(component.commentFieldList[0].value).toBe('draft value'); // Should preserve draft value
      expect(component.commentFieldList[1].value).toBe(undefined); // Should use historical value
    }));
  });

  /**
   * Tests for getCommentaryDetails functionality.
   * Verifies correct handling of null, empty, and regular values.
   */
  describe('getCommentaryDetails', () => {
    beforeEach(() => {
      component.portfolioCompanyCommentaryDetails = {
        significantEventsSection: '',
        assessmentSection: 'some assessment',
        exitPlansSection: null,
        impactSection: ''
      };
      component.CustomCommentryValues = [
        { fieldID: 1, value: '' },
        { fieldID: 2, value: 'custom value' }
      ];
    });

    it('should return null for empty string values', () => {
      const result = component.getCommentaryDetails({
        name: CompanyInformationConstants.SignificantEvents,
        fieldID: 0
      });
      expect(result).toBeNull();
    });

    it('should return non-empty values as is', () => {
      const result = component.getCommentaryDetails({
        name: CompanyInformationConstants.AssessmentPlan,
        fieldID: 0
      });
      expect(result).toBe('some assessment');
    });

    it('should return empty string for null values', () => {
      const result = component.getCommentaryDetails({
        name: CompanyInformationConstants.ExitPlan,
        fieldID: 0
      });
      expect(result).toBe('');
    });

    it('should handle custom field empty values correctly', () => {
      const result = component.getCommentaryDetails({
        name: CompanyInformationConstants.Customfield,
        fieldID: 1
      });
      expect(result).toBeUndefined();
    });

    it('should handle custom field non-empty values correctly', () => {
      const result = component.getCommentaryDetails({
        name: CompanyInformationConstants.Customfield,
        fieldID: 2
      });
      expect(result).toBe('custom value');
    });
  });

  /**
   * Tests for comment input validation behavior
   */
  describe('OnInputAddComment', () => {
    it('should set disableAddCommentDoneBtn to true for empty comment', () => {
      component.OnInputAddComment('');
      expect(component.disableAddCommentDoneBtn).toBeTrue();
    });

    it('should set disableAddCommentDoneBtn to true for null comment', () => {
      component.OnInputAddComment(null);
      expect(component.disableAddCommentDoneBtn).toBeTrue();
    });

    it('should set disableAddCommentDoneBtn to true for whitespace-only comment', () => {
      component.OnInputAddComment('   ');
      expect(component.disableAddCommentDoneBtn).toBeTrue();
    });

    it('should set disableAddCommentDoneBtn to false for valid comment', () => {
      component.OnInputAddComment('Valid comment');
      expect(component.disableAddCommentDoneBtn).toBeFalse();
    });
  });

  /**
   * Tests for showCommentPopup behavior
   */
  describe('showCommentPopup', () => {
    it('should reset comment and disable button when opening popup', () => {
      // Set some initial values
      component.comment = 'Existing comment';
      component.disableAddCommentDoneBtn = false;
      
      // Call the method to open popup
      component.showCommentPopup(false);
      
      // Verify popup is shown and values are reset
      expect(component.showAddCommentPopup).toBeTrue();
      expect(component.comment).toBe('');
      expect(component.disableAddCommentDoneBtn).toBeTrue();
    });

    it('should not change state when isOpen is true', () => {
      // Set some initial values
      component.showAddCommentPopup = false;
      component.comment = 'Existing comment';
      
      // Call with isOpen=true, which should not trigger changes
      component.showCommentPopup(true);
      
      // Verify no changes were made
      expect(component.showAddCommentPopup).toBeFalse();
      expect(component.comment).toBe('Existing comment');
    });
  });

  /**
   * Tests for saveCommentry handling of null values
   */
  describe('saveCommentry null handling', () => {
    beforeEach(() => {
      // Set up common test data
      component.CustomCommentryValues = [];
      component.model = { portfolioCompanyID: 1 };
      component.portfolioCompanyCommentaryDetails = { encryptedCommentaryID: '123' };
      workflowCompanyService.updateStatusCommentry.and.returnValue(of({}));
      workflowCompanyService.saveCompanyCommentaryDetails.and.returnValue(of({ message: 'Success' }));
    });

    it('should convert null values to empty strings when saving', fakeAsync(() => {
      const mockEvent = new MouseEvent('click');
      const mockCommentSection = {
        name: CompanyInformationConstants.SignificantEvents,
        value: null,
        fieldID: 1,
        isEdit: true
      };
      component.commentFieldList = [mockCommentSection];
      
      component.saveCommentry(mockEvent, 0);
      tick();

      // Verify that the API was called with an empty string, not null
      const calledWith = workflowCompanyService.saveCompanyCommentaryDetails.calls.mostRecent().args[0];
      expect(calledWith.significantEventsSection).toBe('');
      expect(mockCommentSection.isEdit).toBeFalse();
    }));
  });

  /**
   * Tests for historical commentary retrieval with null values
   */
  describe('getPortfolioCompaniesHistoricalCommemtary with null values', () => {
    it('should preserve null values in draft data', fakeAsync(() => {
      const mockHistoricalData = {
        result: {
          significantEventsSection: 'historical event'
        },
        customcommentary: []
      };

      workflowCompanyService.getPortfolioCompanyCommentaryHistoricalSections.and.returnValue(of(mockHistoricalData));

      // Set up a field with null value
      component.commentFieldList = [
        { 
          fieldID: 1, 
          name: CompanyInformationConstants.SignificantEvents, 
          value: null 
        }
      ];

      component.getPortfolioCompaniesHistoricalCommemtary();
      tick();

      // The null value should be preserved since it was a draft value
      expect(component.commentFieldList[0].value).toBeNull();
      expect(component.commentFieldList[0].isDraft).toBeTrue();
    }));
  });

  /**
   * Tests for resetAddCommentPopup behavior
   */
  describe('resetAddCommentPopup', () => {
    beforeEach(() => {
    
      
      // Initialize workflowDetails @Input property
      component.workflowDetails = {
        workflowSectionLevels: [
          {
            subFeatureId: UserSubFeaturesEnum.Commentary,
            isRework: false,
            isApprove: true
          }
        ],
        isEnd: false,
        isStart: false
      };
      
      // Initialize dataInformation with a mock entry for the Commentary subfeature
      component.dataInformation = [
        {
          subFeatureId: UserSubFeaturesEnum.Commentary,
          workflowRequestId: 1,
          workflowMappingId: 1,
          isMarkedForReview: true,
          isValidUser: true,
          statusId: 1
        }
      ];
      
      // Ensure setToggleStatus is ready
      component.sectionData = component.workflowDetails.workflowSectionLevels[0];
    });

    it('should reset comment to empty string', () => {
      // Set initial value
      component.comment = 'Some comment text';
      
      // Call the method
      component.resetAddCommentPopup();
      
      // Verify comment is reset
      expect(component.comment).toBe('');
      expect(component.disableAddCommentDoneBtn).toBeTrue();
      expect(component.showAddCommentPopup).toBeFalse();
    });
    
    it('should call setToggleStatus', () => {
      // Spy on the method
      spyOn(component, 'setToggleStatus');
      
      // Call the method
      component.resetAddCommentPopup();
      
      // Verify setToggleStatus was called
      expect(component.setToggleStatus).toHaveBeenCalled();
    });
  });

});
