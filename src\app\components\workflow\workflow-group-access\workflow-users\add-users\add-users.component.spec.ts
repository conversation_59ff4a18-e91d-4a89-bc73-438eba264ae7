import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormsModule } from '@angular/forms';
import { AddUsersComponent } from './add-users.component';
import { ToastrService } from 'ngx-toastr';
import { AccountService } from 'src/app/services/account.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { OidcAuthService } from './../../../../../services/oidc-auth.service';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { FileUploadService } from 'src/app/services/file-upload.service';
import { CUSTOM_ELEMENTS_SCHEMA, ChangeDetectorRef } from '@angular/core';
import { of } from 'rxjs';
import { HttpResponse } from '@angular/common/http';

describe('AddUsersComponent', () => {
  let component: AddUsersComponent;
  let fixture: ComponentFixture<AddUsersComponent>;
  let accountService: AccountService;

  beforeEach(() => {
    const fileUploadServicestub = {
      exportTemplates: () => { }
    };

    const oidcAuthServicestub = {
      addUsersFromBeatIds: () => {},
      updateUsersFromBeatIds: () => {},
      isUserExistFromBeatIds: () => {},
      setActiveUserInBeatIds: () => {},
      setDeActiveUserInBeatIds: object => ({ subscribe: f => f({}) })
    }
    const miscellaneousServiceStub = {
      getCountryList: () => of({ body: ['Country1', 'Country2'] }),
      GetUsersFromBeatIds: () => of({ body: ['User1', 'User2'] }),
      downloadExcelFile: () => of({}),
    };
    const toastrServiceStub = {
      success: () => { },
      error: () => { }
    };
    const accountserviceStub = {
      addUser: object => ({ subscribe: f => f({}) }),
      updateUser: object => ({ subscribe: f => f({}) }),
      addupdateSessionId: jasmine.createSpy('addupdateSessionId').and.returnValue({ subscribe: f => f({}) })
    }
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, HttpClientTestingModule,FormsModule],
      declarations: [AddUsersComponent],
      providers: [
        { provide: ToastrService, useValue: toastrServiceStub },
        { provide: MiscellaneousService, useValue: miscellaneousServiceStub },
        { provide: AccountService, useValue:accountserviceStub },
        { provide: OidcAuthService, useValue: oidcAuthServicestub },
        { provide: FileUploadService, useValue: fileUploadServicestub },
        { provide: ChangeDetectorRef, useValue: jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']) },
        { provide: 'BASE_URL', useValue: 'http://localhost:4200/' },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    });
    
    fixture = TestBed.createComponent(AddUsersComponent);
    component = fixture.componentInstance;
    accountService = TestBed.inject(AccountService);
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`defaultPlaceholder has default value`, () => {
    expect(component.defaultPlaceholder).toEqual(`Browse`);
  });

  it(`uploadFilePlaceholder has default value`, () => {
    expect(component.uploadFilePlaceholder).toEqual(component.defaultPlaceholder);
  });

  it(`browseicon has default value`, () => {
    expect(component.browseicon).toEqual(true);
  });

  it(`files has default value`, () => {
    expect(component.files).toEqual([]);
  });

  it(`uploadedFiles has default value`, () => {
    expect(component.uploadedFiles).toEqual([]);
  });

  it(`messages has default value`, () => {
    expect(component.messages).toEqual([]);
  });

  it(`FileProgresStatus has default value`, () => {
    expect(component.FileProgresStatus).toEqual(`Cancel File Progress`);
  });

  it(`value has default value`, () => {
    expect(component.value).toEqual(0);
  });

  it(`cancel has default value`, () => {
    expect(component.cancel).toEqual(false);
  });

  it(`ProgressCancel has default value`, () => {
    expect(component.ProgressCancel).toEqual(true);
  });

  it(`submitted has default value`, () => {
    expect(component.submitted).toEqual(false);
  });

  it(`countryList has default value`, () => {
    expect(component.countryList).toEqual([]);
  });

  it(`groupList has default value`, () => {
    expect(component.groupList).toEqual([]);
  });

  it(`msgs has default value`, () => {
    expect(component.msgs).toEqual([]);
  });

  it(`title has default value`, () => {
    expect(component.title).toEqual(`Create`);
  });

  it(`resetText has default value`, () => {
    expect(component.resetText).toEqual(`Reset`);
  });

  it(`userStatus has default value`, () => {
    expect(component.userStatus).toEqual(true);
  });

  it(`loading has default value`, () => {
    expect(component.loading).toEqual(false);
  });

  it(`strModuleType has default value`, () => {
    expect(component.strModuleType).toEqual(`UserList`);
  });

  it(`interval has default value`, () => {
    expect(component.interval).toEqual(0);
  });

  it(`messageClass has default value`, () => {
    expect(component.messageClass).toEqual(`bulkMessage`);
  });

  it(`userListIds has default value`, () => {
    expect(component.userListIds).toEqual([]);
  });

  it('should create form with correct controls and validators', () => {
    spyOn(component, 'getCountries');
    spyOn(component, 'GetUsersFromBeatIds');
  
    component.ngOnInit();
  
    expect(component.form instanceof FormGroup).toBe(true);
    expect(Object.keys(component.form.controls)).toEqual([
      'firstName', 'lastName', 'emailId', 'phoneNo', 'country', 'organization', 'isActive'
    ]);
  
    // Test the 'firstName' control
    component.form.get('firstName').setValue('');
    expect(component.form.get('firstName').valid).toBe(false);
    component.form.get('firstName').setValue('John');
    expect(component.form.get('firstName').valid).toBe(true);
     // Test the 'lastName' control
   component.form.get('lastName').setValue('');
   expect(component.form.get('lastName').valid).toBe(false);
   component.form.get('lastName').setValue('Doe');
   expect(component.form.get('lastName').valid).toBe(true);

    // Test the 'emailId' control
  component.form.get('emailId').setValue('');
  expect(component.form.get('emailId').valid).toBe(false);
  component.form.get('emailId').setValue('<EMAIL>');
  expect(component.form.get('emailId').valid).toBe(true);
  
    // Add similar tests for the other controls here...
  
    expect(component.getCountries).toHaveBeenCalled();
    expect(component.GetUsersFromBeatIds).toHaveBeenCalled();
  });

  it('should reset properties when deleteiconclick is called', () => {
    // Arrange
    component.files = ['file1', 'file2'];
    component.uploadFilePlaceholder = 'placeholder';
    component.browseicon = false;
    component.messages = ['message1', 'message2'];
  
    // Act
    component.deleteiconclick();
  
    // Assert
    expect(component.files).toEqual([]);
    expect(component.uploadFilePlaceholder).toBe(component.defaultPlaceholder);
    expect(component.browseicon).toBe(true);
    expect(component.messages).toEqual([]);
  });

  it('should set countryList when getCountries is called', () => {
    // Arrange
    const miscellaneousServiceStub: MiscellaneousService = fixture.debugElement.injector.get(
      MiscellaneousService
    );
    const countryListResponse = { body: ['Country1', 'Country2'] };
    spyOn(miscellaneousServiceStub, 'getCountryList').and.returnValue(of(countryListResponse));
  
    // Act
    component.getCountries();
  
    // Assert
    expect(component.countryList).toEqual(countryListResponse.body);
  });

  it('should return immediately when form is invalid and files length is 0', () => {
    // Arrange
    component.form = { invalid: true } as any;
    component.files = [];
  
    // Act
    component.saveUser(null);
  
    // Assert
    expect(component.submitted).toBeFalsy();
    expect(component.loading).toBeFalsy();
  });

  it('should set submitted and loading to true and call addUser when form is valid', () => {
    // Arrange
    spyOn(component, 'addUser');
    component.form = { valid: true } as any;
    const fakeForm = {};
  
    // Act
    component.saveUser(fakeForm);
  
    // Assert
    expect(component.submitted).toBeTruthy();
    expect(component.loading).toBeTruthy();
    expect(component.addUser).toHaveBeenCalledWith(fakeForm);
  });

  it('should set loading to true and call onUpload when files length is greater than 0', () => {
    // Arrange
    spyOn(component, 'onUpload');
    component.form = { invalid: true } as any;
    component.files = ['file1'];
  
    // Act
    component.saveUser(null);
  
    // Assert
    expect(component.loading).toBeTruthy();
    expect(component.onUpload).toHaveBeenCalled();
  });

  it('should download template when response is ok', () => {
    const fileUploadServicestub: FileUploadService = fixture.debugElement.injector.get(
      FileUploadService
    );
    const miscellaneousServiceStub: MiscellaneousService = fixture.debugElement.injector.get(
      MiscellaneousService
    );
    const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
    const response = { ok: true };
    spyOn(fileUploadServicestub, 'exportTemplates').and.returnValue(of(response as HttpResponse<Blob>));
    spyOn(miscellaneousServiceStub, 'downloadExcelFile').and.callThrough();
    spyOn(toastrServiceStub, 'error').and.callThrough();
    component.downloadTemplate();
  
    expect(miscellaneousServiceStub.downloadExcelFile).toHaveBeenCalled();
    expect(toastrServiceStub.error).not.toHaveBeenCalled();
  });
 
  it('should add user in beat ids', () => {

    const oidcAuthServicestub: OidcAuthService = fixture.debugElement.injector.get(
      OidcAuthService
    );
    const user = {
      country: { country: 'USA' },
      emailID: '<EMAIL>',
      lastName: 'Doe',
      firstName: 'John',
      phoneNumber: '1234567890'
    };
    spyOn(oidcAuthServicestub, 'addUsersFromBeatIds').and.returnValue(of({}));

    component.addUserInBeatIds(user);

    expect(component.userModel.country).toEqual('USA');
    expect(component.userModel.email).toEqual('<EMAIL>');
    expect(component.userModel.lastName).toEqual('Doe');
    expect(component.userModel.firstName).toEqual('John');
    expect(component.userModel.phoneNumber).toEqual('1234567890');
    expect(oidcAuthServicestub.addUsersFromBeatIds).toHaveBeenCalledWith(component.userModel);
  });

  it('should update user in beat ids', () => {
    const oidcAuthServicestub: OidcAuthService = fixture.debugElement.injector.get(
      OidcAuthService
    );
    spyOn(oidcAuthServicestub, 'updateUsersFromBeatIds').and.returnValue(of({}));

    component.updateUserInBeatIds();

    expect(oidcAuthServicestub.updateUsersFromBeatIds).toHaveBeenCalledWith({});
  });

  it('should Exist user in beat ids', () => {
    const oidcAuthServicestub: OidcAuthService = fixture.debugElement.injector.get(
      OidcAuthService
    );
    spyOn(oidcAuthServicestub, 'isUserExistFromBeatIds').and.returnValue(of({}));

    component.isExistUsersInBeatIds();

    expect(oidcAuthServicestub.isUserExistFromBeatIds).toHaveBeenCalledWith('');
  });

  it('should set Active user in beat ids', () => {
    const oidcAuthServicestub: OidcAuthService = fixture.debugElement.injector.get(
      OidcAuthService
    );
    spyOn(oidcAuthServicestub, 'setActiveUserInBeatIds').and.returnValue(of({}));

    component.setActiveUserInBeatIds();

    expect(oidcAuthServicestub.setActiveUserInBeatIds).toHaveBeenCalledWith('');
  });

  it('should set InActive user in beat ids', () => {
    const oidcAuthServicestub: OidcAuthService = fixture.debugElement.injector.get(
      OidcAuthService
    );
    spyOn(oidcAuthServicestub, 'setDeActiveUserInBeatIds').and.returnValue(of({}));

    component.setInActiveUserInBeatIds();

    expect(oidcAuthServicestub.setDeActiveUserInBeatIds).toHaveBeenCalledWith('');
  });

  it('should upload users for IDs', () => {
    const oidcAuthServicestub: OidcAuthService = fixture.debugElement.injector.get(
      OidcAuthService
    );
    const usersModel = [
      { statusDescription: '', country: { country: 'USA' } },
      { statusDescription: 'not empty', country: { country: 'USA' } },
      { statusDescription: '', country: { country: 'USA' } },
    ];
  
    spyOn(oidcAuthServicestub, 'addUsersFromBeatIds').and.returnValue(of({}));
    spyOn(component, 'addUserInBeatIds').and.callThrough();
  
    component.uploadUsersForIDs(usersModel);
  
    expect(component.addUserInBeatIds).toHaveBeenCalledTimes(2);
  });

  it('should handle file larger than 20 MB', () => {
    const files = [{ size: 20000001, name: 'largeFile.txt' }];
    component.onSelect(files);

    expect(component.uploadResultobj.safeHtml).toEqual('File size is greater than 20 MB.');
    expect(component.fileUploader.files).toEqual([]);
  });

  it('should handle file smaller than 20 MB', () => {
    const files = [{ size: ********, name: 'smallFile.txt' }];
    component.onSelect(files);

    expect(component.ProgressCancel).toBeTrue();
    expect(component.value).toEqual(0);
    expect(component.cancel).toBeFalse();
    expect(component.FileProgresStatus).toEqual('Cancel File Progress');
    expect(component.uploadFilePlaceholder).toEqual('smallFile.txt');
    expect(component.browseicon).toBeFalse();
  });

  it('should add user', () => {
    component.title = 'Create';
    const accountServiceStub: AccountService = fixture.debugElement.injector.get(AccountService);
    const toastrService: ToastrService = fixture.debugElement.injector.get(ToastrService);
    spyOn(accountServiceStub, 'addUser').and.returnValue(of({ code: 'OK' }));
    spyOn(toastrService, 'success').and.callThrough();
    spyOn(component, 'formReset');
    spyOn(component.onUserChanges, 'emit');
    component.addUser({});

    expect(component.loading).toBeFalse();
    expect(component.submitted).toBeFalse();
    expect(toastrService.success).toHaveBeenCalled();
    expect(component.onUserChanges.emit).toHaveBeenCalledWith(true);
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should update user', () => {
    component.title = 'Update';
    const toastrService: ToastrService = fixture.debugElement.injector.get(ToastrService);
    const accountServiceStub: AccountService = fixture.debugElement.injector.get(AccountService);
    spyOn(accountServiceStub, 'updateUser').and.returnValue(of({ code: 'OK' }));
    spyOn(toastrService, 'success').and.callThrough();
    spyOn(component.onUserChanges, 'emit');
    component.addUser({});

    expect(component.loading).toBeFalse();
    expect(component.submitted).toBeFalse();
    expect(toastrService.success).toHaveBeenCalled();
    expect(component.onUserChanges.emit).toHaveBeenCalledWith(true);
  });

});