import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { AccountService } from 'src/app/services/account.service';
import { LazyLoadEvent } from 'primeng/api/lazyloadevent';
import { ToastrService } from 'ngx-toastr';
import { FeaturesEnum } from 'src/app/services/permission.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormsModule } from '@angular/forms';
import { WorkflowUsersComponent } from './workflow-users.component';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { of, throwError } from 'rxjs';
import { HttpResponse } from '@angular/common/http';


describe('WorkflowUsersComponent', () => {
  let component: WorkflowUsersComponent;
  let fixture: ComponentFixture<WorkflowUsersComponent>;
  let miscServiceStub;


  beforeEach(() => {
    const accountServiceStub = {
      getUserList: object => ({ subscribe: f => f({}) }),
      UpdateUserSubGroups: usersToBeUpdated => ({ subscribe: f => f({}) }),
      exportUserList: object => ({ subscribe: f => f({}) }),
      addupdateSessionId: jasmine.createSpy('addupdateSessionId').and.returnValue({ subscribe: f => f({}) })
    };
    const toastrServiceStub = {
      overlayContainer: () => ({}),
      success: (string, string1, object) => ({}),
      error: (message, string, object) => ({})
    };
    const miscServiceStub = {
      downloadExcelFile: () => {},
    }
    TestBed.configureTestingModule({
      imports: [FormsModule, HttpClientTestingModule],
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [WorkflowUsersComponent],
      providers: [
        { provide: AccountService, useValue: accountServiceStub },
        { provide: ToastrService, useValue: toastrServiceStub },
        { provide: MiscellaneousService, useValue: miscServiceStub },
        { provide: 'BASE_URL', useValue: 'http://localhost:4200/' },

      ]
    });
    fixture = TestBed.createComponent(WorkflowUsersComponent);
    component = fixture.componentInstance;
    component.GroupDetails = { groupName: '' };
  });

  it('can load instance', () => {
    expect(component).toBeTruthy();
  });

  it(`feature has default value`, () => {
    expect(component.feature).toEqual(FeaturesEnum);
  });

  it(`users has default value`, () => {
    expect(component.users).toEqual([]);
  });

  it(`blockedTable has default value`, () => {
    expect(component.blockedTable).toEqual(false);
  });

  it(`checkAll has default value`, () => {
    expect(component.checkAll).toEqual(false);
  });

  it(`usersToBeUpdated has default value`, () => {
    expect(component.usersToBeUpdated).toEqual([]);
  });

  it(`selectedSubGroupId has default value`, () => {
    expect(component.selectedSubGroupId).toEqual(0);
  });

  it(`allUsers has default value`, () => {
    expect(component.allUsers).toEqual([]);
  });
  
  it(`isOpenAddUser has default value`, () => {
    expect(component.isOpenAddUser).toEqual(false);
  });
  it(`loading has default value`, () => {
    expect(component.loading).toEqual(false);
  });

    it('should export user list', () => {
      const response = {}; // replace with the actual response you expect
      const accountServiceStub: AccountService = fixture.debugElement.injector.get(AccountService);
      const miscServiceStub: MiscellaneousService = fixture.debugElement.injector.get(MiscellaneousService);
      spyOn(accountServiceStub, 'exportUserList').and.returnValue(of(response as HttpResponse<Blob>));
      spyOn(miscServiceStub, 'downloadExcelFile').and.callThrough();
      component.exportUserList();
  
      expect(accountServiceStub.exportUserList).toHaveBeenCalled();
      expect(miscServiceStub.downloadExcelFile).toHaveBeenCalled();
    });
  
  it('should edit user', () => {
    const user = {}; // replace with the actual user object you expect

    component.editUser(user);

    expect(component.updateUser).toBe(user);
    expect(component.isOpenAddUser).toBeTrue();
  });

  it('should load users when userChanges is true', () => {
    spyOn(component, 'getUserList');
    component.loadUsers(true);

    expect(component.getUserList).toHaveBeenCalledWith(null);
  });

  it('should not load users when userChanges is false', () => {
    spyOn(component, 'getUserList');
    component.loadUsers(false);

    expect(component.getUserList).not.toHaveBeenCalled();
  });

  it('should close user popup', () => {
    component.closeUserPopup({});

    expect(component.isOpenAddUser).toBeFalse();
  });

  it('should return false when selectedSubGroupId > 0 and usersToBeUpdated.length > 0', () => {
    component.selectedSubGroupId = 1;
    component.usersToBeUpdated = [{}]; // replace with actual user objects

    const result = component.setMapButton();

    expect(result).toBeFalse();
  });

  it('should return true when selectedSubGroupId <= 0 or usersToBeUpdated.length <= 0', () => {
    component.selectedSubGroupId = 0;
    component.usersToBeUpdated = [];

    const result = component.setMapButton();

    expect(result).toBeTrue();
  });

  it('should reset user mapping', () => {
    component.selectedSubGroupId = 1; // replace with actual sub group id
    component.usersToBeUpdated = [{}]; // replace with actual user objects
    component.checkAll = true;
    spyOn(component, 'getUsers');

    component.resetUserMapping();

    expect(component.users).toEqual(component.allUsers);
    expect(component.getUsers).toHaveBeenCalledWith({groupID: component.selectedSubGroupId}, true);
    expect(component.usersToBeUpdated).toEqual([]);
    expect(component.checkAll).toBeFalse();
  });

  it('should map users and handle response', () => {
    const response = {}; // replace with the actual response you expect
    const accountServiceStub: AccountService = fixture.debugElement.injector.get(AccountService);
    const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
    spyOn(component, 'getUserList');
    spyOn(accountServiceStub, 'UpdateUserSubGroups').and.returnValue(of(response));
    spyOn(toastrServiceStub, 'success');

    component.mapUsers();

    expect(accountServiceStub.UpdateUserSubGroups).toHaveBeenCalledWith(component.usersToBeUpdated);
    expect(component.getUserList).toHaveBeenCalledWith(null);
    expect(toastrServiceStub.success).toHaveBeenCalled();
    expect(component.loading).toBeFalse();
    expect(component.checkAll).toBeFalse();
  });

  it('should handle error', () => {
    const error = { message: 'Error' };
    const accountServiceStub: AccountService = fixture.debugElement.injector.get(AccountService);
    const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
    spyOn(accountServiceStub, 'UpdateUserSubGroups').and.returnValue(throwError(error));
    spyOn(component, 'resetUserMapping');
    spyOn(toastrServiceStub, 'error');
    component.mapUsers();

    expect(component.resetUserMapping).toHaveBeenCalled();
    expect(toastrServiceStub.error).toHaveBeenCalledWith(error.message, "", { positionClass: "toast-center-center" });
    expect(component.loading).toBeFalse();
  });

  // it('should sort user list', () => {
  //   const users = [
  //     { isSelected: false },
  //     { isSelected: true },
  //     { isSelected: false },
  //     { isSelected: true }
  //   ]; // replace with actual user objects

  //   const sortedUsers = component.sortUserList(users);

  //   expect(sortedUsers[0].isSelected).toBeTrue();
  //   expect(sortedUsers[1].isSelected).toBeTrue();
  //   expect(sortedUsers[2].isSelected).toBeFalse();
  //   expect(sortedUsers[3].isSelected).toBeFalse();
  // });

  it('should get users and keep filter', () => {
    const groupDetails = { groupID: 2 }; // replace with actual group details
    const keepFilter = true;
  
    // Mock users as an array of objects with necessary properties
    component.users = [
      { isSelected: false, groupDetails: [{ groupID: 1 }] },
      { isSelected: true, groupDetails: [{ groupID: 2 }] }
    ];
  
    const originalUsers = [...component.users]; // copy the original users array
  
    spyOn(component, 'sortUserList').and.callThrough();
  
    component.getUsers(groupDetails, keepFilter);
  
    expect(component.selectedSubGroupId).toBe(groupDetails.groupID);
    expect(component.sortUserList).toHaveBeenCalled();
    expect(component.users).toEqual(component.sortUserList(originalUsers));
  });

  it('should load users lazily', () => {
    const event: LazyLoadEvent = {}; // replace with actual event
    spyOn(component, 'getUserList').and.callThrough();
    component.loadUsersLazy(event);

    expect(component.getUserList).toHaveBeenCalledWith(event);
  });

  it('should get user list', () => {
    const event: any = {}; // replace with actual event
    const accountService: AccountService = fixture.debugElement.injector.get(AccountService);
    const response = {
      body: {
        userList: [{}], // replace with actual user list
        totalRecords: 1
      },
      code: 'OK'
    };
   spyOn(accountService, 'getUserList').and.returnValue(of(response));

    component.getUserList(event);

    expect(accountService.getUserList).toHaveBeenCalledWith({ paginationFilter: event });
    expect(component.users).toEqual(response.body.userList);
    expect(component.allUsers).toEqual(response.body.userList);
    expect(component.totalRecords).toEqual(response.body.totalRecords);
    expect(component.blockedTable).toBeFalse();
  });

  it('should handle changes', () => {
    component.GroupDetails = { groupID: 1 }; // replace with actual group details
    component.groupData = { groupID: 2 }; // replace with actual group data
    spyOn(component, 'getUsers').and.callThrough();
    component.ngOnChanges();

    expect(component.getUsers).toHaveBeenCalledWith(component.GroupDetails, false);
    expect(component.groupData).toEqual(component.GroupDetails);
  });

  it('should not handle changes when group IDs are equal', () => {
    component.GroupDetails = { groupID: 1 }; // replace with actual group details
    component.groupData = { groupID: 1 }; // replace with actual group data
    spyOn(component, 'getUsers').and.callThrough();
    component.ngOnChanges();

    expect(component.getUsers).not.toHaveBeenCalled();
    expect(component.groupData).toEqual({ groupID: 1 });
  });
  it('should handle check all', () => {
    component.handleCheckBox(null, 'checkAll');

    expect(component.usersToBeUpdated.length).toEqual(0);
    expect(component.checkAll).toBeTrue();
  });
  it('should handle individual check', () => {
    const data = { userID: 0, isSelected: false, groupDetails: [] };
    component.handleCheckBox(data, 'individual');

    expect(component.usersToBeUpdated.length).toEqual(0);
    expect(component.checkAll).toBeTrue();
  });

  it('should set overlayContainer on init', () => {
    const toastrServiceStub: ToastrService = fixture.debugElement.injector.get(ToastrService);
    component.ngOnInit();
  
    expect(toastrServiceStub.overlayContainer).toBe(component.toastContainer);
  });
  });

